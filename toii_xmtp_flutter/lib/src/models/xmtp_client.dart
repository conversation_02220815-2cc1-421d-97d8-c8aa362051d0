/// XMTP Client model for Flutter plugin
class XmtpClient {
  final String inboxId;
  final String installationId;
  final String environment;
  final String dbPath;
  final Map<String, dynamic> publicIdentity;

  const XmtpClient({
    required this.inboxId,
    required this.installationId,
    required this.environment,
    required this.dbPath,
    required this.publicIdentity,
  }) : assert(inboxId != '', 'inboxId cannot be empty'),
       assert(installationId != '', 'installationId cannot be empty'),
       assert(environment != '', 'environment cannot be empty'),
       assert(dbPath != '', 'dbPath cannot be empty');

  factory XmtpClient.fromMap(Map<String, dynamic> map) {
    return XmtpClient(
      inboxId: map['inboxId'] as String? ?? '',
      installationId: map['installationId'] as String? ?? '',
      environment: map['environment'] as String? ?? 'dev',
      dbPath: map['dbPath'] as String? ?? '',
      publicIdentity: Map<String, dynamic>.from(
        map['publicIdentity'] as Map? ?? {},
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'inboxId': inboxId,
      'installationId': installationId,
      'environment': environment,
      'dbPath': dbPath,
      'publicIdentity': publicIdentity,
    };
  }

  @override
  String toString() {
    return 'XmtpClient(inboxId: $inboxId, installationId: $installationId, environment: $environment)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is XmtpClient &&
        other.inboxId == inboxId &&
        other.installationId == installationId &&
        other.environment == environment;
  }

  @override
  int get hashCode {
    return inboxId.hashCode ^ installationId.hashCode ^ environment.hashCode;
  }
}

/// Client options for creating XMTP client
class ClientOptions {
  final String environment;
  final String? appVersion;
  final String? dbDirectory;
  final String? historySyncUrl;
  final bool deviceSyncEnabled;
  final bool debugEventsEnabled;

  const ClientOptions({
    this.environment = 'dev',
    this.appVersion,
    this.dbDirectory,
    this.historySyncUrl,
    this.deviceSyncEnabled = true,
    this.debugEventsEnabled = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'environment': environment,
      'appVersion': appVersion,
      'dbDirectory': dbDirectory,
      'historySyncUrl': historySyncUrl,
      'deviceSyncEnabled': deviceSyncEnabled,
      'debugEventsEnabled': debugEventsEnabled,
    };
  }

  factory ClientOptions.fromMap(Map<String, dynamic> map) {
    return ClientOptions(
      environment: map['environment'] as String? ?? 'dev',
      appVersion: map['appVersion'] as String?,
      dbDirectory: map['dbDirectory'] as String?,
      historySyncUrl: map['historySyncUrl'] as String?,
      deviceSyncEnabled: map['deviceSyncEnabled'] as bool? ?? true,
      debugEventsEnabled: map['debugEventsEnabled'] as bool? ?? false,
    );
  }
}

/// XMTP Environment enum
enum XmtpEnvironment {
  dev,
  production,
  local;

  String get value {
    switch (this) {
      case XmtpEnvironment.dev:
        return 'dev';
      case XmtpEnvironment.production:
        return 'production';
      case XmtpEnvironment.local:
        return 'local';
    }
  }

  static XmtpEnvironment fromString(String value) {
    switch (value.toLowerCase()) {
      case 'dev':
        return XmtpEnvironment.dev;
      case 'production':
        return XmtpEnvironment.production;
      case 'local':
        return XmtpEnvironment.local;
      default:
        throw ArgumentError('Invalid environment: $value');
    }
  }
}
