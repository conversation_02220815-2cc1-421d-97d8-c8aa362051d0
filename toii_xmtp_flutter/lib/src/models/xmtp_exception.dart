/// XMTP specific exception
class XmtpException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const XmtpException(this.message, {this.code, this.details});

  @override
  String toString() {
    if (code != null) {
      return 'XmtpException($code): $message';
    }
    return 'XmtpException: $message';
  }

  factory XmtpException.fromMap(Map<String, dynamic> map) {
    return XmtpException(
      map['message'] as String,
      code: map['code'] as String?,
      details: map['details'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'code': code,
      'details': details,
    };
  }
}

/// Client creation exception
class ClientCreationException extends XmtpException {
  const ClientCreationException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Conversation exception
class ConversationException extends XmtpException {
  const ConversationException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Message exception
class MessageException extends XmtpException {
  const MessageException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Network exception
class NetworkException extends XmtpException {
  const NetworkException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Authentication exception
class AuthenticationException extends XmtpException {
  const AuthenticationException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Permission exception
class PermissionException extends XmtpException {
  const PermissionException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}
