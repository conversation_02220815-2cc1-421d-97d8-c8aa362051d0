import 'conversation.dart';

/// Group conversation
class Group implements Conversation {
  @override
  final String id;

  @override
  final String topic;

  @override
  final DateTime createdAt;

  @override
  final ConsentState consentState;

  @override
  final bool isActive;

  final String? name;
  final String? description;
  final String? imageUrl;
  final List<String> memberInboxIds;
  final List<String> adminInboxIds;
  final List<String> superAdminInboxIds;
  final String creatorInboxId;
  final GroupPermissions permissions;
  final DisappearingMessageSettings? disappearingMessageSettings;

  const Group({
    required this.id,
    required this.topic,
    required this.createdAt,
    required this.consentState,
    required this.isActive,
    this.name,
    this.description,
    this.imageUrl,
    required this.memberInboxIds,
    required this.adminInboxIds,
    required this.superAdminInboxIds,
    required this.creatorInboxId,
    required this.permissions,
    this.disappearingMessageSettings,
  }) : assert(id != '', 'id cannot be empty'),
       assert(topic != '', 'topic cannot be empty'),
       assert(creatorInboxId != '', 'creatorInboxId cannot be empty');

  @override
  ConversationType get type => ConversationType.group;

  /// Check if user is admin
  bool isAdmin(String inboxId) => adminInboxIds.contains(inboxId);

  /// Check if user is super admin
  bool isSuperAdmin(String inboxId) => superAdminInboxIds.contains(inboxId);

  /// Check if user is creator
  bool isCreator(String inboxId) => creatorInboxId == inboxId;

  /// Get peer inbox IDs (excluding current user)
  List<String> peerInboxIds(String currentUserInboxId) {
    return memberInboxIds.where((id) => id != currentUserInboxId).toList();
  }

  /// Check if disappearing messages are enabled
  bool get isDisappearingMessagesEnabled => disappearingMessageSettings != null;

  factory Group.fromMap(Map<String, dynamic> map) {
    return Group(
      id: map['id'] as String,
      topic: map['topic'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int),
      consentState: ConsentState.fromString(map['consentState'] as String),
      isActive: map['isActive'] as bool,
      name: map['name'] as String?,
      description: map['description'] as String?,
      imageUrl: map['imageUrl'] as String?,
      memberInboxIds: List<String>.from(map['memberInboxIds'] as List),
      adminInboxIds: List<String>.from(map['adminInboxIds'] as List),
      superAdminInboxIds: List<String>.from(map['superAdminInboxIds'] as List),
      creatorInboxId: map['creatorInboxId'] as String,
      permissions: GroupPermissions.fromMap(
        Map<String, dynamic>.from(map['permissions'] as Map),
      ),
      disappearingMessageSettings:
          map['disappearingMessageSettings'] != null
              ? DisappearingMessageSettings.fromMap(
                Map<String, dynamic>.from(
                  map['disappearingMessageSettings'] as Map,
                ),
              )
              : null,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'topic': topic,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'consentState': consentState.value,
      'isActive': isActive,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'memberInboxIds': memberInboxIds,
      'adminInboxIds': adminInboxIds,
      'superAdminInboxIds': superAdminInboxIds,
      'creatorInboxId': creatorInboxId,
      'permissions': permissions.toMap(),
      'disappearingMessageSettings': disappearingMessageSettings?.toMap(),
      'type': type.value,
    };
  }

  @override
  String toString() {
    return 'Group(id: $id, name: $name, memberCount: ${memberInboxIds.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Group && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Group permissions configuration
class GroupPermissions {
  final PermissionOption addMemberPermission;
  final PermissionOption removeMemberPermission;
  final PermissionOption updateNamePermission;
  final PermissionOption updateDescriptionPermission;
  final PermissionOption updateImagePermission;

  const GroupPermissions({
    required this.addMemberPermission,
    required this.removeMemberPermission,
    required this.updateNamePermission,
    required this.updateDescriptionPermission,
    required this.updateImagePermission,
  });

  factory GroupPermissions.fromMap(Map<String, dynamic> map) {
    return GroupPermissions(
      addMemberPermission: PermissionOption.fromString(
        map['addMemberPermission'] as String,
      ),
      removeMemberPermission: PermissionOption.fromString(
        map['removeMemberPermission'] as String,
      ),
      updateNamePermission: PermissionOption.fromString(
        map['updateNamePermission'] as String,
      ),
      updateDescriptionPermission: PermissionOption.fromString(
        map['updateDescriptionPermission'] as String,
      ),
      updateImagePermission: PermissionOption.fromString(
        map['updateImagePermission'] as String,
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'addMemberPermission': addMemberPermission.value,
      'removeMemberPermission': removeMemberPermission.value,
      'updateNamePermission': updateNamePermission.value,
      'updateDescriptionPermission': updateDescriptionPermission.value,
      'updateImagePermission': updateImagePermission.value,
    };
  }
}

/// Permission options for group actions
enum PermissionOption {
  allow,
  deny,
  adminOnly,
  superAdminOnly;

  String get value {
    switch (this) {
      case PermissionOption.allow:
        return 'allow';
      case PermissionOption.deny:
        return 'deny';
      case PermissionOption.adminOnly:
        return 'admin_only';
      case PermissionOption.superAdminOnly:
        return 'super_admin_only';
    }
  }

  static PermissionOption fromString(String value) {
    switch (value.toLowerCase()) {
      case 'allow':
        return PermissionOption.allow;
      case 'deny':
        return PermissionOption.deny;
      case 'admin_only':
        return PermissionOption.adminOnly;
      case 'super_admin_only':
        return PermissionOption.superAdminOnly;
      default:
        throw ArgumentError('Invalid permission option: $value');
    }
  }
}

/// Disappearing message settings
class DisappearingMessageSettings {
  final DateTime disappearStartingAt;
  final Duration retentionDuration;

  const DisappearingMessageSettings({
    required this.disappearStartingAt,
    required this.retentionDuration,
  });

  factory DisappearingMessageSettings.fromMap(Map<String, dynamic> map) {
    return DisappearingMessageSettings(
      disappearStartingAt: DateTime.fromMillisecondsSinceEpoch(
        map['disappearStartingAt'] as int,
      ),
      retentionDuration: Duration(
        milliseconds: map['retentionDuration'] as int,
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'disappearStartingAt': disappearStartingAt.millisecondsSinceEpoch,
      'retentionDuration': retentionDuration.inMilliseconds,
    };
  }
}
