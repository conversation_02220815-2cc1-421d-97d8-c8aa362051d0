import 'src/models/models.dart';
import 'toii_xmtp_flutter_platform_interface.dart';

// Export models for public use
export 'src/models/models.dart';

class ToiiXmtpFlutter {
  Future<String?> getPlatformVersion() {
    return ToiiXmtpFlutterPlatform.instance.getPlatformVersion();
  }

  // Client methods
  Future<XmtpClient> createClient({
    required String privateKey,
    ClientOptions? options,
  }) {
    return ToiiXmtpFlutterPlatform.instance.createClient(
      privateKey: privateKey,
      options: options,
    );
  }

  Future<String> getInboxId() {
    return ToiiXmtpFlutterPlatform.instance.getInboxId();
  }

  Future<String> getInboxIdFromIdentity(
    String identity, {
    String environment = 'dev',
  }) {
    return ToiiXmtpFlutterPlatform.instance.getInboxIdFromIdentity(
      identity,
      environment: environment,
    );
  }

  // Conversation methods
  Future<List<Conversation>> listConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) {
    return ToiiXmtpFlutterPlatform.instance.listConversations(
      includeGroups: includeGroups,
      includeDms: includeDms,
    );
  }

  Future<void> syncConversations() {
    return ToiiXmtpFlutterPlatform.instance.syncConversations();
  }

  Stream<Conversation> streamConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) {
    return ToiiXmtpFlutterPlatform.instance.streamConversations(
      includeGroups: includeGroups,
      includeDms: includeDms,
    );
  }

  // Group methods
  Future<Group> createGroup({
    required List<String> memberInboxIds,
    String? groupName,
    String? groupDescription,
    String? groupImageUrl,
  }) {
    return ToiiXmtpFlutterPlatform.instance.createGroup(
      memberInboxIds: memberInboxIds,
      groupName: groupName,
      groupDescription: groupDescription,
      groupImageUrl: groupImageUrl,
    );
  }

  Future<List<Group>> listGroups() {
    return ToiiXmtpFlutterPlatform.instance.listGroups();
  }

  Future<void> syncGroup(String groupId) {
    return ToiiXmtpFlutterPlatform.instance.syncGroup(groupId);
  }

  Future<void> addMembers(String groupId, List<String> memberInboxIds) {
    return ToiiXmtpFlutterPlatform.instance.addMembers(groupId, memberInboxIds);
  }

  Future<void> removeMembers(String groupId, List<String> memberInboxIds) {
    return ToiiXmtpFlutterPlatform.instance.removeMembers(
      groupId,
      memberInboxIds,
    );
  }

  Future<void> addAdmin(String groupId, String inboxId) {
    return ToiiXmtpFlutterPlatform.instance.addAdmin(groupId, inboxId);
  }

  Future<void> removeAdmin(String groupId, String inboxId) {
    return ToiiXmtpFlutterPlatform.instance.removeAdmin(groupId, inboxId);
  }

  Future<void> addSuperAdmin(String groupId, String inboxId) {
    return ToiiXmtpFlutterPlatform.instance.addSuperAdmin(groupId, inboxId);
  }

  Future<void> removeSuperAdmin(String groupId, String inboxId) {
    return ToiiXmtpFlutterPlatform.instance.removeSuperAdmin(groupId, inboxId);
  }

  Future<void> updateGroupName(String groupId, String name) {
    return ToiiXmtpFlutterPlatform.instance.updateGroupName(groupId, name);
  }

  Future<void> updateGroupDescription(String groupId, String description) {
    return ToiiXmtpFlutterPlatform.instance.updateGroupDescription(
      groupId,
      description,
    );
  }

  Future<void> updateGroupImageUrl(String groupId, String imageUrl) {
    return ToiiXmtpFlutterPlatform.instance.updateGroupImageUrl(
      groupId,
      imageUrl,
    );
  }

  Future<void> updateConsentState(String groupId, ConsentState consentState) {
    return ToiiXmtpFlutterPlatform.instance.updateConsentState(
      groupId,
      consentState,
    );
  }

  Future<void> updateDisappearingMessageSettings(
    String groupId,
    DisappearingMessageSettings settings,
  ) {
    return ToiiXmtpFlutterPlatform.instance.updateDisappearingMessageSettings(
      groupId,
      settings,
    );
  }

  Future<void> clearDisappearingMessageSettings(String groupId) {
    return ToiiXmtpFlutterPlatform.instance.clearDisappearingMessageSettings(
      groupId,
    );
  }

  // DM methods
  Future<Dm> findOrCreateDm(String targetInboxId) {
    return ToiiXmtpFlutterPlatform.instance.findOrCreateDm(targetInboxId);
  }

  Future<List<Dm>> listDms() {
    return ToiiXmtpFlutterPlatform.instance.listDms();
  }

  // Message methods
  Future<String> sendMessage({
    required String conversationId,
    required String content,
    SendOptions? options,
  }) {
    return ToiiXmtpFlutterPlatform.instance.sendMessage(
      conversationId: conversationId,
      content: content,
      options: options,
    );
  }

  Future<List<XmtpMessage>> getMessages(
    String conversationId, {
    MessageQueryOptions? options,
  }) {
    return ToiiXmtpFlutterPlatform.instance.getMessages(
      conversationId,
      options: options,
    );
  }

  Future<List<MessageWithReactions>> getMessagesWithReactions(
    String conversationId, {
    int limit = 50,
  }) {
    return ToiiXmtpFlutterPlatform.instance.getMessagesWithReactions(
      conversationId,
      limit: limit,
    );
  }

  Stream<XmtpMessage> streamMessages(String conversationId) {
    return ToiiXmtpFlutterPlatform.instance.streamMessages(conversationId);
  }

  Future<String> prepareMessage(String conversationId, String content) {
    return ToiiXmtpFlutterPlatform.instance.prepareMessage(
      conversationId,
      content,
    );
  }

  Future<void> publishMessages(String conversationId) {
    return ToiiXmtpFlutterPlatform.instance.publishMessages(conversationId);
  }
}
