import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'src/models/models.dart';
import 'toii_xmtp_flutter_method_channel.dart';

abstract class ToiiXmtpFlutterPlatform extends PlatformInterface {
  /// Constructs a ToiiXmtpFlutterPlatform.
  ToiiXmtpFlutterPlatform() : super(token: _token);

  static final Object _token = Object();

  static ToiiXmtpFlutterPlatform _instance = MethodChannelToiiXmtpFlutter();

  /// The default instance of [ToiiXmtpFlutterPlatform] to use.
  ///
  /// Defaults to [MethodChannelToiiXmtpFlutter].
  static ToiiXmtpFlutterPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [ToiiXmtpFlutterPlatform] when
  /// they register themselves.
  static set instance(ToiiXmtpFlutterPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  // Platform info
  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  // Client methods
  Future<XmtpClient> createClient({
    required String privateKey,
    ClientOptions? options,
  }) {
    throw UnimplementedError('createClient() has not been implemented.');
  }

  Future<String> getInboxId() {
    throw UnimplementedError('getInboxId() has not been implemented.');
  }

  Future<String> getInboxIdFromIdentity(
    String identity, {
    String environment = 'dev',
  }) {
    throw UnimplementedError(
      'getInboxIdFromIdentity() has not been implemented.',
    );
  }

  // Conversation methods
  Future<List<Conversation>> listConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) {
    throw UnimplementedError('listConversations() has not been implemented.');
  }

  Future<void> syncConversations() {
    throw UnimplementedError('syncConversations() has not been implemented.');
  }

  Stream<Conversation> streamConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) {
    throw UnimplementedError('streamConversations() has not been implemented.');
  }

  // Group methods
  Future<Group> createGroup({
    required List<String> memberInboxIds,
    String? groupName,
    String? groupDescription,
    String? groupImageUrl,
  }) {
    throw UnimplementedError('createGroup() has not been implemented.');
  }

  Future<List<Group>> listGroups() {
    throw UnimplementedError('listGroups() has not been implemented.');
  }

  Future<void> syncGroup(String groupId) {
    throw UnimplementedError('syncGroup() has not been implemented.');
  }

  Future<void> addMembers(String groupId, List<String> memberInboxIds) {
    throw UnimplementedError('addMembers() has not been implemented.');
  }

  Future<void> removeMembers(String groupId, List<String> memberInboxIds) {
    throw UnimplementedError('removeMembers() has not been implemented.');
  }

  Future<void> addAdmin(String groupId, String inboxId) {
    throw UnimplementedError('addAdmin() has not been implemented.');
  }

  Future<void> removeAdmin(String groupId, String inboxId) {
    throw UnimplementedError('removeAdmin() has not been implemented.');
  }

  Future<void> addSuperAdmin(String groupId, String inboxId) {
    throw UnimplementedError('addSuperAdmin() has not been implemented.');
  }

  Future<void> removeSuperAdmin(String groupId, String inboxId) {
    throw UnimplementedError('removeSuperAdmin() has not been implemented.');
  }

  Future<void> updateGroupName(String groupId, String name) {
    throw UnimplementedError('updateGroupName() has not been implemented.');
  }

  Future<void> updateGroupDescription(String groupId, String description) {
    throw UnimplementedError(
      'updateGroupDescription() has not been implemented.',
    );
  }

  Future<void> updateGroupImageUrl(String groupId, String imageUrl) {
    throw UnimplementedError('updateGroupImageUrl() has not been implemented.');
  }

  Future<void> updateConsentState(String groupId, ConsentState consentState) {
    throw UnimplementedError('updateConsentState() has not been implemented.');
  }

  Future<void> updateDisappearingMessageSettings(
    String groupId,
    DisappearingMessageSettings settings,
  ) {
    throw UnimplementedError(
      'updateDisappearingMessageSettings() has not been implemented.',
    );
  }

  Future<void> clearDisappearingMessageSettings(String groupId) {
    throw UnimplementedError(
      'clearDisappearingMessageSettings() has not been implemented.',
    );
  }

  // DM methods
  Future<Dm> findOrCreateDm(String targetInboxId) {
    throw UnimplementedError('findOrCreateDm() has not been implemented.');
  }

  Future<List<Dm>> listDms() {
    throw UnimplementedError('listDms() has not been implemented.');
  }

  // Message methods
  Future<String> sendMessage({
    required String conversationId,
    required String content,
    SendOptions? options,
  }) {
    throw UnimplementedError('sendMessage() has not been implemented.');
  }

  Future<List<XmtpMessage>> getMessages(
    String conversationId, {
    MessageQueryOptions? options,
  }) {
    throw UnimplementedError('getMessages() has not been implemented.');
  }

  Future<List<MessageWithReactions>> getMessagesWithReactions(
    String conversationId, {
    int limit = 50,
  }) {
    throw UnimplementedError(
      'getMessagesWithReactions() has not been implemented.',
    );
  }

  Stream<XmtpMessage> streamMessages(String conversationId) {
    throw UnimplementedError('streamMessages() has not been implemented.');
  }

  Future<String> prepareMessage(String conversationId, String content) {
    throw UnimplementedError('prepareMessage() has not been implemented.');
  }

  Future<void> publishMessages(String conversationId) {
    throw UnimplementedError('publishMessages() has not been implemented.');
  }
}
