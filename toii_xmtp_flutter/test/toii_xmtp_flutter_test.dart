import 'package:flutter_test/flutter_test.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter_method_channel.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter_platform_interface.dart';

class MockToiiXmtpFlutterPlatform
    with MockPlatformInterfaceMixin
    implements ToiiXmtpFlutterPlatform {
  @override
  Future<String?> getPlatformVersion() => Future.value('42');

  @override
  Future<XmtpClient> createClient({
    required String privateKey,
    ClientOptions? options,
  }) async {
    return XmtpClient(
      inboxId: 'test-inbox-id',
      installationId: 'test-installation-id',
      environment: 'dev',
      dbPath: '/test/path',
      publicIdentity: {'test': 'identity'},
    );
  }

  @override
  Future<String> getInboxId() => Future.value('test-inbox-id');

  @override
  Future<String> getInboxIdFromIdentity(
    String identity, {
    String environment = 'dev',
  }) => Future.value('test-inbox-id-from-identity');

  @override
  Future<List<Conversation>> listConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) => Future.value([]);

  @override
  Future<void> syncConversations() => Future.value();

  @override
  Stream<Conversation> streamConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) => Stream.empty();

  @override
  Future<Group> createGroup({
    required List<String> memberInboxIds,
    String? groupName,
    String? groupDescription,
    String? groupImageUrl,
  }) async {
    return Group(
      id: 'test-group-id',
      topic: 'test-topic',
      createdAt: DateTime.now(),
      consentState: ConsentState.allowed,
      isActive: true,
      name: groupName,
      description: groupDescription,
      imageUrl: groupImageUrl,
      memberInboxIds: memberInboxIds,
      adminInboxIds: [],
      superAdminInboxIds: [],
      creatorInboxId: 'test-creator',
      permissions: const GroupPermissions(
        addMemberPermission: PermissionOption.allow,
        removeMemberPermission: PermissionOption.adminOnly,
        updateNamePermission: PermissionOption.adminOnly,
        updateDescriptionPermission: PermissionOption.adminOnly,
        updateImagePermission: PermissionOption.adminOnly,
      ),
    );
  }

  @override
  Future<List<Group>> listGroups() => Future.value([]);

  @override
  Future<void> syncGroup(String groupId) => Future.value();

  @override
  Future<void> addMembers(String groupId, List<String> memberInboxIds) =>
      Future.value();

  @override
  Future<void> removeMembers(String groupId, List<String> memberInboxIds) =>
      Future.value();

  @override
  Future<void> addAdmin(String groupId, String inboxId) => Future.value();

  @override
  Future<void> removeAdmin(String groupId, String inboxId) => Future.value();

  @override
  Future<void> addSuperAdmin(String groupId, String inboxId) => Future.value();

  @override
  Future<void> removeSuperAdmin(String groupId, String inboxId) =>
      Future.value();

  @override
  Future<void> updateGroupName(String groupId, String name) => Future.value();

  @override
  Future<void> updateGroupDescription(String groupId, String description) =>
      Future.value();

  @override
  Future<void> updateGroupImageUrl(String groupId, String imageUrl) =>
      Future.value();

  @override
  Future<void> updateConsentState(String groupId, ConsentState consentState) =>
      Future.value();

  @override
  Future<void> updateDisappearingMessageSettings(
    String groupId,
    DisappearingMessageSettings settings,
  ) => Future.value();

  @override
  Future<void> clearDisappearingMessageSettings(String groupId) =>
      Future.value();

  @override
  Future<Dm> findOrCreateDm(String targetInboxId) async {
    return Dm(
      id: 'test-dm-id',
      topic: 'test-dm-topic',
      createdAt: DateTime.now(),
      consentState: ConsentState.allowed,
      isActive: true,
      peerInboxId: targetInboxId,
    );
  }

  @override
  Future<List<Dm>> listDms() => Future.value([]);

  @override
  Future<String> sendMessage({
    required String conversationId,
    required String content,
    SendOptions? options,
  }) => Future.value('test-message-id');

  @override
  Future<List<XmtpMessage>> getMessages(
    String conversationId, {
    MessageQueryOptions? options,
  }) => Future.value([]);

  @override
  Future<List<MessageWithReactions>> getMessagesWithReactions(
    String conversationId, {
    int limit = 50,
  }) => Future.value([]);

  @override
  Stream<XmtpMessage> streamMessages(String conversationId) => Stream.empty();

  @override
  Future<String> prepareMessage(String conversationId, String content) =>
      Future.value('test-prepared-message-id');

  @override
  Future<void> publishMessages(String conversationId) => Future.value();
}

void main() {
  final ToiiXmtpFlutterPlatform initialPlatform =
      ToiiXmtpFlutterPlatform.instance;

  test('$MethodChannelToiiXmtpFlutter is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelToiiXmtpFlutter>());
  });

  test('getPlatformVersion', () async {
    ToiiXmtpFlutter toiiXmtpFlutterPlugin = ToiiXmtpFlutter();
    MockToiiXmtpFlutterPlatform fakePlatform = MockToiiXmtpFlutterPlatform();
    ToiiXmtpFlutterPlatform.instance = fakePlatform;

    expect(await toiiXmtpFlutterPlugin.getPlatformVersion(), '42');
  });

  test('createClient with valid private key', () async {
    ToiiXmtpFlutter toiiXmtpFlutterPlugin = ToiiXmtpFlutter();
    MockToiiXmtpFlutterPlatform fakePlatform = MockToiiXmtpFlutterPlatform();
    ToiiXmtpFlutterPlatform.instance = fakePlatform;

    final client = await toiiXmtpFlutterPlugin.createClient(
      privateKey: '0x${'1' * 64}',
      options: const ClientOptions(environment: 'dev'),
    );

    expect(client.inboxId, 'test-inbox-id');
    expect(client.environment, 'dev');
  });

  test('createGroup', () async {
    ToiiXmtpFlutter toiiXmtpFlutterPlugin = ToiiXmtpFlutter();
    MockToiiXmtpFlutterPlatform fakePlatform = MockToiiXmtpFlutterPlatform();
    ToiiXmtpFlutterPlatform.instance = fakePlatform;

    final group = await toiiXmtpFlutterPlugin.createGroup(
      memberInboxIds: ['member1', 'member2'],
      groupName: 'Test Group',
      groupDescription: 'A test group',
    );

    expect(group.name, 'Test Group');
    expect(group.description, 'A test group');
    expect(group.memberInboxIds, ['member1', 'member2']);
  });

  test('sendMessage', () async {
    ToiiXmtpFlutter toiiXmtpFlutterPlugin = ToiiXmtpFlutter();
    MockToiiXmtpFlutterPlatform fakePlatform = MockToiiXmtpFlutterPlatform();
    ToiiXmtpFlutterPlatform.instance = fakePlatform;

    final messageId = await toiiXmtpFlutterPlugin.sendMessage(
      conversationId: 'test-conversation-id',
      content: 'Hello, world!',
    );

    expect(messageId, 'test-message-id');
  });

  test('findOrCreateDm', () async {
    ToiiXmtpFlutter toiiXmtpFlutterPlugin = ToiiXmtpFlutter();
    MockToiiXmtpFlutterPlatform fakePlatform = MockToiiXmtpFlutterPlatform();
    ToiiXmtpFlutterPlatform.instance = fakePlatform;

    final dm = await toiiXmtpFlutterPlugin.findOrCreateDm('target-inbox-id');

    expect(dm.peerInboxId, 'target-inbox-id');
    expect(dm.type, ConversationType.dm);
  });
}
