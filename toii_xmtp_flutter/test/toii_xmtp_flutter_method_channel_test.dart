import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannelToiiXmtpFlutter platform = MethodChannelToiiXmtpFlutter();
  const MethodChannel channel = MethodChannel('toii_xmtp_flutter');

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return '42';
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });
}
