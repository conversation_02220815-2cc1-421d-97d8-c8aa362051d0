package com.toii.ai.toii_xmtp_flutter.handlers

import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.EventChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collect
import org.xmtp.android.library.Client
import org.xmtp.android.library.Dm
import org.xmtp.android.library.Group
import org.xmtp.android.library.libxmtp.DecodedMessage
import org.xmtp.android.library.codecs.id

class MessageHandler {
    
    fun sendMessage(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)
                val content = call.argument<String>("content")
                    ?: return@launch result.error("INVALID_ARGUMENT", "content is required", null)

                Log.d("XMTP", "Sending message to conversation: $conversationId")

                // Find the conversation (DM or Group)
                val conversation = try {
                    // Try to find as DM first
                    client.conversations.listDms().find { it.id == conversationId }
                        ?: client.conversations.listGroups().find { it.id == conversationId }
                } catch (e: Exception) {
                    Log.e("XMTP", "Error finding conversation: ${e.message}")
                    null
                }

                if (conversation == null) {
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }

                // Sync conversation first to ensure we have latest state
                Log.d("XMTP", "Syncing conversation before sending message: $conversationId")
                when (conversation) {
                    is org.xmtp.android.library.Dm -> conversation.sync()
                    is org.xmtp.android.library.Group -> conversation.sync()
                }

                // Send message based on conversation type
                val messageId = when (conversation) {
                    is org.xmtp.android.library.Dm -> {
                        Log.d("XMTP", "Sending DM message: $content")
                        conversation.send(content)
                    }
                    is org.xmtp.android.library.Group -> {
                        Log.d("XMTP", "Sending Group message: $content")
                        conversation.send(content)
                    }
                    else -> {
                        return@launch result.error("UNSUPPORTED_CONVERSATION_TYPE", "Unsupported conversation type", null)
                    }
                }

                Log.d("XMTP", "Message sent successfully with ID: $messageId")
                result.success(messageId)
            } catch (e: Exception) {
                Log.e("XMTP", "Failed to send message: ${e.message}", e)
                result.error("SEND_MESSAGE_ERROR", "Failed to send message: ${e.message}", e.toString())
            }
        }
    }
    
    fun getMessages(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)

                Log.d("XMTP", "Getting messages for conversation: $conversationId")

                // Find the conversation (DM or Group)
                val conversation = try {
                    // Try to find as DM first
                    client.conversations.listDms().find { it.id == conversationId }
                        ?: client.conversations.listGroups().find { it.id == conversationId }
                } catch (e: Exception) {
                    Log.e("XMTP", "Error finding conversation: ${e.message}")
                    null
                }

                if (conversation == null) {
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }

                // Sync conversation first to ensure we have latest messages
                Log.d("XMTP", "Syncing conversation before getting messages: $conversationId")
                when (conversation) {
                    is org.xmtp.android.library.Dm -> conversation.sync()
                    is org.xmtp.android.library.Group -> conversation.sync()
                }

                // Get query options
                val limit = call.argument<Int>("limit") ?: 50
                val beforeNs = call.argument<Long>("beforeNs")
                val afterNs = call.argument<Long>("afterNs")

                Log.d("XMTP", "Retrieving messages with limit: $limit")

                // Get messages based on conversation type
                val messages = when (conversation) {
                    is org.xmtp.android.library.Dm -> {
                        conversation.messages(
                            limit = limit,
                            beforeNs = beforeNs,
                            afterNs = afterNs
                        )
                    }
                    is org.xmtp.android.library.Group -> {
                        conversation.messages(
                            limit = limit,
                            beforeNs = beforeNs,
                            afterNs = afterNs
                        )
                    }
                    else -> {
                        return@launch result.error("UNSUPPORTED_CONVERSATION_TYPE", "Unsupported conversation type", null)
                    }
                }

                // Convert messages to map format
                val messageList = messages.map { message ->
                    mapOf(
                        "id" to message.id,
                        "conversationId" to conversationId,
                        "senderInboxId" to message.senderInboxId,
                        "content" to message.body,
                        "contentType" to message.encodedContent.type.id,
                        "sentAt" to message.sentAt.time,
                        "deliveryStatus" to when (message.deliveryStatus) {
                            org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.PUBLISHED -> "published"
                            org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.UNPUBLISHED -> "unpublished"
                            org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.FAILED -> "failed"
                            else -> "published"
                        }
                    )
                }

                Log.d("XMTP", "Retrieved ${messageList.size} messages")
                result.success(messageList)
            } catch (e: Exception) {
                Log.e("XMTP", "Error getting messages: ${e.message}", e)
                result.error("GET_MESSAGES_ERROR", "Failed to get messages: ${e.message}", e.toString())
            }
        }
    }

    fun listMessages(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch

                // TODO: Implement message listing
                result.success(emptyList<Map<String, Any>>())
            } catch (e: Exception) {
                result.error("LIST_MESSAGES_ERROR", "Failed to list messages: ${e.message}", e.toString())
            }
        }
    }
    
    fun streamMessages(call: MethodCall, result: Result, eventChannel: EventChannel, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)

                Log.d("XMTP", "Setting up message stream for conversation: $conversationId")

                // Find the conversation (DM or Group)
                val conversation = try {
                    // Try to find as DM first
                    client.conversations.listDms().find { it.id == conversationId }
                        ?: client.conversations.listGroups().find { it.id == conversationId }
                } catch (e: Exception) {
                    Log.e("XMTP", "Error finding conversation: ${e.message}")
                    null
                }

                if (conversation == null) {
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }

                // Sync conversation first to ensure we have latest state
                Log.d("XMTP", "Syncing conversation before starting stream: $conversationId")
                when (conversation) {
                    is org.xmtp.android.library.Dm -> conversation.sync()
                    is org.xmtp.android.library.Group -> conversation.sync()
                }

                // Set up event channel stream handler
                eventChannel.setStreamHandler(object : EventChannel.StreamHandler {
                    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                        scope.launch {
                            try {
                                Log.d("XMTP", "Starting message stream for conversation: $conversationId")

                                // Stream messages based on conversation type
                                when (conversation) {
                                    is org.xmtp.android.library.Dm -> {
                                        conversation.streamMessages().collect { message ->
                                            val messageData = mapOf(
                                                "id" to message.id,
                                                "conversationId" to conversationId,
                                                "senderInboxId" to message.senderInboxId,
                                                "content" to message.body,
                                                "contentType" to message.encodedContent.type.id,
                                                "sentAt" to message.sentAt.time,
                                                "deliveryStatus" to when (message.deliveryStatus) {
                                                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.PUBLISHED -> "published"
                                                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.UNPUBLISHED -> "unpublished"
                                                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.FAILED -> "failed"
                                                    else -> "published"
                                                }
                                            )
                                            events?.success(messageData)
                                        }
                                    }
                                    is org.xmtp.android.library.Group -> {
                                        conversation.streamMessages().collect { message ->
                                            val messageData = mapOf(
                                                "id" to message.id,
                                                "conversationId" to conversationId,
                                                "senderInboxId" to message.senderInboxId,
                                                "content" to message.body,
                                                "contentType" to message.encodedContent.type.id,
                                                "sentAt" to message.sentAt.time,
                                                "deliveryStatus" to when (message.deliveryStatus) {
                                                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.PUBLISHED -> "published"
                                                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.UNPUBLISHED -> "unpublished"
                                                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.FAILED -> "failed"
                                                    else -> "published"
                                                }
                                            )
                                            events?.success(messageData)
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e("XMTP", "Error in message stream: ${e.message}", e)
                                events?.error("STREAM_ERROR", "Message stream error: ${e.message}", e.toString())
                            }
                        }
                    }

                    override fun onCancel(arguments: Any?) {
                        Log.d("XMTP", "Message stream cancelled for conversation: $conversationId")
                    }
                })

                result.success(true)
                Log.d("XMTP", "Message stream setup completed for conversation: $conversationId")
            } catch (e: Exception) {
                Log.e("XMTP", "Error setting up message stream: ${e.message}", e)
                result.error("STREAM_MESSAGES_ERROR", "Failed to setup message stream: ${e.message}", e.toString())
            }
        }
    }

    private fun getClientFromCall(call: MethodCall, result: Result): Client? {
        return ClientHandler.getClient() ?: run {
            result.error("NO_CLIENT", "Client not initialized", null)
            null
        }
    }
}


