package com.toii.ai.toii_xmtp_flutter

import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.EventChannel.StreamHandler
import kotlinx.coroutines.*
import com.toii.ai.toii_xmtp_flutter.handlers.*

/** ToiiXmtpFlutterPlugin */
class ToiiXmtpFlutterPlugin: FlutterPlugin, MethodCallHandler, StreamHandler {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private lateinit var methodChannel: MethodChannel
  private lateinit var eventChannel: EventChannel

  // Coroutine scope for async operations
  private val mainScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

  // Handlers for different XMTP operations
  private lateinit var clientHandler: ClientHandler
  private lateinit var conversationHandler: ConversationHandler
  private lateinit var messageHandler: MessageHandler
  private lateinit var groupHandler: GroupHandler
  private lateinit var dmHandler: DmHandler

  override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    methodChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "toii_xmtp_flutter")
    methodChannel.setMethodCallHandler(this)

    eventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "toii_xmtp_flutter_events")
    eventChannel.setStreamHandler(this)

    // Initialize handlers
    clientHandler = ClientHandler(flutterPluginBinding.applicationContext)
    conversationHandler = ConversationHandler()
    messageHandler = MessageHandler()
    groupHandler = GroupHandler()
    dmHandler = DmHandler()
  }

  override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
    when (call.method) {
      // Platform info
      "getPlatformVersion" -> result.success("Android ${android.os.Build.VERSION.RELEASE}")

      // Client methods
      "createClient" -> clientHandler.createClient(call, result, mainScope)
      "getInboxId" -> clientHandler.getInboxId(call, result)
      "getInboxIdFromIdentity" -> clientHandler.getInboxIdFromIdentity(call, result, mainScope)

      // Conversation methods
      "listConversations" -> conversationHandler.listConversations(call, result, mainScope)
      "syncConversations" -> conversationHandler.syncConversations(call, result, mainScope)
      "streamConversations" -> conversationHandler.streamConversations(call, result, eventChannel, mainScope)

      // Group methods
      "createGroup" -> groupHandler.createGroup(call, result, mainScope)
      "syncGroup" -> groupHandler.syncGroup(call, result, mainScope)
      "addMembers" -> groupHandler.addMembers(call, result, mainScope)
      "removeMembers" -> groupHandler.removeMembers(call, result, mainScope)

      // DM methods
      "findOrCreateDm" -> dmHandler.findOrCreateDm(call, result, mainScope)
      "listDms" -> dmHandler.listDms(call, result, mainScope)

      // Message methods
      "sendMessage" -> messageHandler.sendMessage(call, result, mainScope)
      "getMessages" -> messageHandler.getMessages(call, result, mainScope)
      "listMessages" -> messageHandler.listMessages(call, result, mainScope)
      "streamMessages" -> messageHandler.streamMessages(call, result, eventChannel, mainScope)

      else -> result.notImplemented()
    }
  }

  override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
    methodChannel.setMethodCallHandler(null)
    eventChannel.setStreamHandler(null)
    mainScope.cancel()
  }

  // StreamHandler implementation for EventChannel
  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    // For now, just acknowledge the stream setup
    // TODO: Implement actual message streaming
    events?.success(mapOf("status" to "stream_started"))
  }

  override fun onCancel(arguments: Any?) {
    // Handle stream cancellation
    // TODO: Clean up any active streams
  }
}
