# Hướng dẫn Chuyển đổi Ví và Kết nối XMTP

## 🔧 Vấn đề đã được khắc phục

Trư<PERSON><PERSON> đây, sau khi lưu ví, bạn có thể gặp các vấn đề sau:
- <PERSON>h<PERSON>ng thể kết nối XMTP với ví mới
- <PERSON>hông thể tạo inbox với ví khác
- Client bị "stuck" với ví cũ
- Lỗi "Client already exists"

## ✅ Giải pháp đã triển khai

### 1. **Client Cleanup tự động**
- <PERSON><PERSON> chuyển ví, client cũ được cleanup tự động
- State được reset hoàn toàn
- Không còn xung đột database

### 2. **Wallet Switching cải tiến**
- X<PERSON><PERSON> nhận trước khi chuyển ví nếu có client đang hoạt động
- Cleanup an toàn trước khi load ví mới
- <PERSON><PERSON><PERSON> nhật UI rõ ràng về trạng thái

### 3. **Error Handling tốt hơn**
- Th<PERSON>ng báo lỗi cụ thể hơn
- Hướng dẫn khắc phục rõ ràng
- Retry logic tự động

## 📋 Cách sử dụng mới

### Bước 1: Lưu ví hiện tại
1. Nhập private key
2. Nhập tên ví
3. Nhấn "Save" - ví sẽ được lưu với inbox ID nếu đã kết nối

### Bước 2: Chuyển đổi ví
1. Nhấn "Show" để hiển thị danh sách ví đã lưu
2. Nhấn biểu tượng download để chuyển ví
3. Xác nhận nếu có client đang hoạt động
4. Ví mới sẽ được load, client cũ sẽ bị disconnect

### Bước 3: Kết nối với ví mới
1. Sau khi load ví, nhấn "Create Client"
2. Đợi kết nối XMTP network
3. Client mới sẽ được tạo với inbox ID mới
4. Conversations sẽ được load tự động

### Bước 4: Disconnect khi cần
1. Nhấn "Disconnect" để ngắt kết nối client hiện tại
2. Xác nhận trong dialog
3. Client sẽ được cleanup an toàn

## 🚨 Lưu ý quan trọng

### Khi nào cần disconnect?
- Trước khi chuyển sang ví khác
- Khi gặp lỗi kết nối
- Khi muốn reset trạng thái client

### Tại sao cần cleanup?
- XMTP client sử dụng database path dựa trên private key
- Mỗi ví cần có client riêng biệt
- Tránh xung đột dữ liệu giữa các ví

### Troubleshooting

#### Lỗi "Client already exists"
**Giải pháp:** 
1. Nhấn "Disconnect" trước
2. Đợi 1-2 giây
3. Nhấn "Create Client" lại

#### Không thể tạo DM với ví khác
**Giải pháp:**
1. Đảm bảo đã disconnect client cũ
2. Load ví mới
3. Tạo client mới
4. Thử tạo DM lại

#### Conversations không load
**Giải pháp:**
1. Kiểm tra kết nối mạng
2. Disconnect và reconnect client
3. Restart app nếu cần

## 🔄 Quy trình khuyến nghị

### Lần đầu sử dụng:
1. Tạo ví mới hoặc nhập private key
2. Lưu ví với tên dễ nhớ
3. Tạo client để kết nối XMTP
4. Bắt đầu chat

### Chuyển đổi ví:
1. Disconnect client hiện tại (nếu có)
2. Load ví mới từ danh sách
3. Tạo client mới
4. Kiểm tra conversations

### Khi gặp vấn đề:
1. Disconnect client
2. Restart app
3. Load ví lại
4. Tạo client mới

## 💡 Tips

- **Luôn lưu ví** trước khi tạo client để có thể quay lại sau
- **Disconnect trước khi chuyển ví** để tránh xung đột
- **Kiểm tra inbox ID** sau khi tạo client thành công
- **Sử dụng tên ví có ý nghĩa** để dễ quản lý

## 🆘 Hỗ trợ

Nếu vẫn gặp vấn đề:
1. Kiểm tra logs trong console
2. Thử restart app
3. Xóa và tạo lại ví nếu cần
4. Liên hệ support với thông tin lỗi cụ thể
