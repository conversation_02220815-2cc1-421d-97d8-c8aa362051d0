import 'package:equatable/equatable.dart';

/// Model for saved wallet data
class SavedWallet extends Equatable {
  final String name;
  final String privateKey;
  final String? inboxId;
  final DateTime createdAt;
  final DateTime? lastUsed;

  const SavedWallet({
    required this.name,
    required this.privateKey,
    this.inboxId,
    required this.createdAt,
    this.lastUsed,
  });

  /// Create from JSON map
  factory SavedWallet.fromJson(Map<String, dynamic> json) {
    return SavedWallet(
      name: json['name'] as String,
      privateKey: json['privateKey'] as String,
      inboxId: json['inboxId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUsed: json['lastUsed'] != null 
          ? DateTime.parse(json['lastUsed'] as String)
          : null,
    );
  }

  /// Convert to JSON map
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'privateKey': privateKey,
      'inboxId': inboxId,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed?.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  SavedWallet copyWith({
    String? name,
    String? privateKey,
    String? inboxId,
    DateTime? createdAt,
    DateTime? lastUsed,
  }) {
    return SavedWallet(
      name: name ?? this.name,
      privateKey: privateKey ?? this.privateKey,
      inboxId: inboxId ?? this.inboxId,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }

  /// Get display name with creation date
  String get displayName {
    final dateStr = createdAt.toString().substring(0, 16);
    return '$name ($dateStr)';
  }

  /// Get truncated inbox ID for display
  String get displayInboxId {
    if (inboxId == null) return 'Not connected';
    return '${inboxId!.substring(0, 8)}...';
  }

  @override
  List<Object?> get props => [name, privateKey, inboxId, createdAt, lastUsed];
}
