import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart'
    hide MessageDeliveryStatus;
import 'package:uuid/uuid.dart';

import '../models/chat_models.dart';
import 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  final ToiiXmtpFlutter _xmtpPlugin;
  final String _currentUserInboxId;
  StreamSubscription<XmtpMessage>? _messageStreamSubscription;
  final List<ChatMessage> _messageCache = [];

  ChatCubit({
    required ToiiXmtpFlutter xmtpPlugin,
    required String currentUserInboxId,
  }) : _xmtpPlugin = xmtpPlugin,
       _currentUserInboxId = currentUserInboxId,
       super(const ChatInitial());

  /// Load chat conversation and messages
  Future<void> loadChat(ChatConversation conversation) async {
    try {
      emit(const ChatLoading());

      // Sync conversation first to ensure we have latest data
      await _syncConversation(conversation.id);

      // Load message history
      final messages = await _loadMessages(conversation.id);

      emit(ChatLoaded(conversation: conversation, messages: messages));

      // Start message streaming
      await _startMessageStreaming(conversation.id);
    } catch (e) {
      emit(
        ChatError(
          message: 'Failed to load chat: $e',
          conversationId: conversation.id,
        ),
      );
    }
  }

  /// Sync conversation to ensure latest data
  Future<void> _syncConversation(String conversationId) async {
    try {
      // Sync conversations to get latest data from network
      await _xmtpPlugin.syncConversations();

      // For groups, also sync the specific group
      // We try to sync as group and ignore errors for DMs since DMs are synced
      // through the general conversation sync above
      try {
        await _xmtpPlugin.syncGroup(conversationId);
      } catch (e) {
        // This is expected for DMs - they don't have individual group sync
        // DMs are synced through the general conversations.sync() call above
      }
    } catch (e) {
      // Log but don't throw - sync failures shouldn't prevent chat loading
      print('Warning: Failed to sync conversation $conversationId: $e');
    }
  }

  /// Load messages for a conversation
  Future<List<ChatMessage>> _loadMessages(String conversationId) async {
    try {
      final xmtpMessages = await _xmtpPlugin.getMessages(conversationId);

      final chatMessages = <ChatMessage>[];
      for (final msg in xmtpMessages) {
        try {
          final chatMessage = ChatMessage.fromXmtpMessage(
            msg,
            _currentUserInboxId,
          );
          chatMessages.add(chatMessage);
        } catch (e) {
          print('Error converting message ${msg.id}: $e');
          // Skip invalid messages and continue processing
        }
      }

      // Sort by sent time (newest first for chat UI)
      chatMessages.sort((a, b) => b.sentAt.compareTo(a.sentAt));

      // Update cache
      _messageCache.clear();
      _messageCache.addAll(chatMessages);

      return chatMessages;
    } catch (e) {
      print('Error loading messages: $e');
      return [];
    }
  }

  /// Send a message
  Future<void> sendMessage(String content) async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      // Sync conversation before sending to ensure we have latest state
      await _syncConversation(currentState.conversation.id);

      // Create pending message
      final pendingMessageId = const Uuid().v4();
      final pendingMessage = ChatMessage(
        id: pendingMessageId,
        conversationId: currentState.conversation.id,
        senderInboxId: _currentUserInboxId,
        content: content,
        sentAt: DateTime.now(),
        deliveryStatus: MessageDeliveryStatus.pending,
        isFromCurrentUser: true,
      );

      // Add pending message to UI
      final updatedMessages = [pendingMessage, ...currentState.messages];
      emit(
        ChatSendingMessage(
          conversation: currentState.conversation,
          messages: updatedMessages,
          pendingMessageId: pendingMessageId,
          isStreaming: currentState.isStreaming,
        ),
      );

      // Send message via XMTP
      final sentMessageId = await _xmtpPlugin.sendMessage(
        conversationId: currentState.conversation.id,
        content: content,
      );

      // Update message with sent status
      final sentMessage = pendingMessage.copyWith(
        id: sentMessageId,
        deliveryStatus: MessageDeliveryStatus.sent,
      );

      final finalMessages =
          updatedMessages.where((msg) => msg.id != pendingMessageId).toList();
      finalMessages.insert(0, sentMessage);

      emit(
        ChatMessageSent(
          conversation: currentState.conversation,
          messages: finalMessages,
          sentMessageId: sentMessageId,
          isStreaming: currentState.isStreaming,
        ),
      );
    } catch (e) {
      // Handle send error - mark message as failed
      final currentStateAfterError = state;
      if (currentStateAfterError is ChatSendingMessage) {
        final failedMessages =
            currentStateAfterError.messages.map((msg) {
              if (msg.id == currentStateAfterError.pendingMessageId) {
                return msg.copyWith(
                  deliveryStatus: MessageDeliveryStatus.failed,
                );
              }
              return msg;
            }).toList();

        emit(
          ChatLoaded(
            conversation: currentStateAfterError.conversation,
            messages: failedMessages,
            isStreaming: currentStateAfterError.isStreaming,
          ),
        );
      }

      emit(
        ChatError(
          message: 'Failed to send message: $e',
          conversationId: currentState.conversation.id,
        ),
      );
    }
  }

  /// Start message streaming
  Future<void> _startMessageStreaming(String conversationId) async {
    try {
      await _stopMessageStreaming();

      // Ensure conversation is synced before starting stream
      await _syncConversation(conversationId);

      final messageStream = _xmtpPlugin.streamMessages(conversationId);
      _messageStreamSubscription = messageStream.listen(
        (xmtpMessage) => _handleNewMessage(xmtpMessage),
        onError: (error) {
          print('Message streaming error: $error');
          // Try to restart streaming after a delay on error
          Future.delayed(const Duration(seconds: 5), () {
            if (!isClosed) {
              _startMessageStreaming(conversationId);
            }
          });
        },
        cancelOnError:
            false, // Don't cancel stream on individual message errors
      );

      final currentState = state;
      if (currentState is ChatLoaded) {
        emit(
          ChatStreamingStarted(
            conversation: currentState.conversation,
            messages: currentState.messages,
          ),
        );
      }
    } catch (e) {
      print('Failed to start message streaming: $e');
    }
  }

  /// Handle new message from stream
  void _handleNewMessage(XmtpMessage xmtpMessage) {
    try {
      final currentState = state;
      if (currentState is! ChatLoaded) return;

      // Skip if message is from current user (already handled by send)
      if (xmtpMessage.senderInboxId == _currentUserInboxId) return;

      // Check if message already exists
      final existingMessage = currentState.messages.any(
        (msg) => msg.id == xmtpMessage.id,
      );
      if (existingMessage) return;

      final newMessage = ChatMessage.fromXmtpMessage(
        xmtpMessage,
        _currentUserInboxId,
      );
      final updatedMessages = [newMessage, ...currentState.messages];

      emit(
        ChatMessageReceived(
          conversation: currentState.conversation,
          messages: updatedMessages,
          newMessage: newMessage,
          isStreaming: currentState.isStreaming,
        ),
      );
    } catch (e) {
      print('Error handling new message: $e');
      // Don't emit error state for individual message failures
      // Just log the error and continue
    }
  }

  /// Stop message streaming
  Future<void> _stopMessageStreaming() async {
    await _messageStreamSubscription?.cancel();
    _messageStreamSubscription = null;

    final currentState = state;
    if (currentState is ChatLoaded && currentState.isStreaming) {
      emit(
        ChatStreamingStopped(
          conversation: currentState.conversation,
          messages: currentState.messages,
        ),
      );
    }
  }

  /// Refresh messages
  Future<void> refreshMessages() async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      // Sync conversation before refreshing messages
      await _syncConversation(currentState.conversation.id);

      final messages = await _loadMessages(currentState.conversation.id);
      emit(currentState.copyWith(messages: messages));
    } catch (e) {
      emit(
        ChatError(
          message: 'Failed to refresh messages: $e',
          conversationId: currentState.conversation.id,
        ),
      );
    }
  }

  @override
  Future<void> close() async {
    await _stopMessageStreaming();
    return super.close();
  }
}

/// Extension to add copyWith method to ChatMessage
extension ChatMessageCopyWith on ChatMessage {
  ChatMessage copyWith({
    String? id,
    String? conversationId,
    String? senderInboxId,
    String? content,
    DateTime? sentAt,
    MessageDeliveryStatus? deliveryStatus,
    bool? isFromCurrentUser,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      senderInboxId: senderInboxId ?? this.senderInboxId,
      content: content ?? this.content,
      sentAt: sentAt ?? this.sentAt,
      deliveryStatus: deliveryStatus ?? this.deliveryStatus,
      isFromCurrentUser: isFromCurrentUser ?? this.isFromCurrentUser,
    );
  }
}
