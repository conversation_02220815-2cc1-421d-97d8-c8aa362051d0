import 'package:flutter/foundation.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

/// Diagnostic utilities for debugging conversation issues
class ConversationDiagnostics {
  static const String _tag = 'ConversationDiagnostics';

  /// Validate conversation ID format
  static bool isValidConversationId(String conversationId) {
    if (conversationId.isEmpty) return false;
    
    // XMTP conversation IDs are typically hex strings
    final hexRegex = RegExp(r'^[a-fA-F0-9]+$');
    return hexRegex.hasMatch(conversationId) && 
           conversationId.length >= 8 && 
           conversationId.length <= 128;
  }

  /// Diagnose conversation lookup issues
  static Future<ConversationDiagnosticResult> diagnoseConversation({
    required ToiiXmtpFlutter xmtpPlugin,
    required String conversationId,
  }) async {
    final result = ConversationDiagnosticResult(conversationId: conversationId);
    
    debugPrint('$_tag: Starting diagnosis for conversation: $conversationId');
    
    // 1. Validate conversation ID format
    result.isValidFormat = isValidConversationId(conversationId);
    if (!result.isValidFormat) {
      result.issues.add('Invalid conversation ID format');
      debugPrint('$_tag: Invalid conversation ID format: $conversationId');
    }

    try {
      // 2. Try to list all conversations
      debugPrint('$_tag: Listing all conversations...');
      final conversations = await xmtpPlugin.listConversations();
      result.totalConversations = conversations.length;
      
      // 3. Check if conversation exists in the list
      final foundConversation = conversations.any((conv) => conv.id == conversationId);
      result.foundInList = foundConversation;
      
      if (!foundConversation) {
        result.issues.add('Conversation not found in conversation list');
        debugPrint('$_tag: Conversation not found in list of ${conversations.length} conversations');
        
        // Log available conversation IDs for debugging
        debugPrint('$_tag: Available conversations:');
        for (final conv in conversations.take(5)) { // Show first 5
          debugPrint('$_tag:   - ${conv.id} (${conv.type.value})');
        }
        if (conversations.length > 5) {
          debugPrint('$_tag:   ... and ${conversations.length - 5} more');
        }
      } else {
        debugPrint('$_tag: Conversation found in list');
      }

      // 4. Try to sync conversations
      debugPrint('$_tag: Syncing conversations...');
      await xmtpPlugin.syncConversations();
      result.syncSuccessful = true;
      
      // 5. Check again after sync
      final conversationsAfterSync = await xmtpPlugin.listConversations();
      result.foundAfterSync = conversationsAfterSync.any((conv) => conv.id == conversationId);
      
      if (result.foundAfterSync && !result.foundInList) {
        result.issues.add('Conversation found only after sync - sync timing issue');
        debugPrint('$_tag: Conversation found after sync but not before');
      }

    } catch (e) {
      result.issues.add('Error during diagnosis: $e');
      debugPrint('$_tag: Error during diagnosis: $e');
    }

    // 6. Generate recommendations
    result.recommendations = _generateRecommendations(result);
    
    debugPrint('$_tag: Diagnosis complete. Issues: ${result.issues.length}');
    return result;
  }

  static List<String> _generateRecommendations(ConversationDiagnosticResult result) {
    final recommendations = <String>[];
    
    if (!result.isValidFormat) {
      recommendations.add('Check conversation ID format - should be a hex string');
    }
    
    if (!result.foundInList && !result.foundAfterSync) {
      recommendations.add('Conversation does not exist - verify it was created properly');
      recommendations.add('Check if you\'re using the correct conversation ID');
    }
    
    if (!result.foundInList && result.foundAfterSync) {
      recommendations.add('Sync conversations before attempting operations');
      recommendations.add('Consider adding retry logic with sync');
    }
    
    if (!result.syncSuccessful) {
      recommendations.add('Fix sync issues - check network connectivity');
      recommendations.add('Verify XMTP client is properly initialized');
    }
    
    if (result.totalConversations == 0) {
      recommendations.add('No conversations found - check if client is properly set up');
    }
    
    return recommendations;
  }
}

/// Result of conversation diagnostic check
class ConversationDiagnosticResult {
  final String conversationId;
  bool isValidFormat = false;
  bool foundInList = false;
  bool foundAfterSync = false;
  bool syncSuccessful = false;
  int totalConversations = 0;
  final List<String> issues = [];
  List<String> recommendations = [];

  ConversationDiagnosticResult({required this.conversationId});

  bool get hasIssues => issues.isNotEmpty;
  
  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('Conversation Diagnostic Report for: $conversationId');
    buffer.writeln('Valid Format: $isValidFormat');
    buffer.writeln('Found in List: $foundInList');
    buffer.writeln('Found After Sync: $foundAfterSync');
    buffer.writeln('Sync Successful: $syncSuccessful');
    buffer.writeln('Total Conversations: $totalConversations');
    
    if (issues.isNotEmpty) {
      buffer.writeln('\nIssues:');
      for (final issue in issues) {
        buffer.writeln('  - $issue');
      }
    }
    
    if (recommendations.isNotEmpty) {
      buffer.writeln('\nRecommendations:');
      for (final rec in recommendations) {
        buffer.writeln('  - $rec');
      }
    }
    
    return buffer.toString();
  }
}
