import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

import 'models/chat_models.dart';
import 'models/saved_wallet.dart';
import 'screens/chat_screen.dart';
import 'services/wallet_storage_service.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'XMTP v3 Flutter Demo',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const XmtpHomePage(),
    );
  }
}

class XmtpHomePage extends StatefulWidget {
  const XmtpHomePage({super.key});

  @override
  State<XmtpHomePage> createState() => _XmtpHomePageState();
}

class _XmtpHomePageState extends State<XmtpHomePage> {
  String _platformVersion = 'Unknown';
  final _toiiXmtpFlutterPlugin = ToiiXmtpFlutter();

  XmtpClient? _client;
  List<Conversation> _conversations = [];
  List<SavedWallet> _savedWallets = [];
  SavedWallet? _currentWallet;

  final _privateKeyController = TextEditingController();
  final _messageController = TextEditingController();
  final _targetInboxIdController = TextEditingController();
  final _groupNameController = TextEditingController();
  final _memberInboxIdsController = TextEditingController();
  final _walletNameController = TextEditingController();

  bool _isLoading = false;
  String _status = 'Ready';
  bool _showSavedWallets = false;

  @override
  void initState() {
    super.initState();
    initPlatformState();
    _loadSavedWallets();

    // Generate a random private key for testing
    _privateKeyController.text = _generateRandomPrivateKey();
  }

  @override
  void dispose() {
    _privateKeyController.dispose();
    _messageController.dispose();
    _targetInboxIdController.dispose();
    _groupNameController.dispose();
    _memberInboxIdsController.dispose();
    _walletNameController.dispose();
    super.dispose();
  }

  /// Load saved wallets from storage
  Future<void> _loadSavedWallets() async {
    try {
      final wallets = await WalletStorageService.getSavedWallets();
      setState(() {
        _savedWallets = wallets;
      });
    } catch (e) {
      print('Error loading saved wallets: $e');
    }
  }

  /// Save current wallet configuration
  Future<void> _saveCurrentWallet() async {
    if (_privateKeyController.text.isEmpty) {
      _showError('Please enter a private key first');
      return;
    }

    if (_walletNameController.text.isEmpty) {
      _showError('Please enter a wallet name');
      return;
    }

    try {
      final wallet = SavedWallet(
        name: _walletNameController.text.trim(),
        privateKey: _privateKeyController.text.trim(),
        inboxId: _client?.inboxId,
        createdAt: DateTime.now(),
        lastUsed: _client != null ? DateTime.now() : null,
      );

      final success = await WalletStorageService.saveWallet(wallet);
      if (success) {
        await _loadSavedWallets();
        _walletNameController.clear();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Wallet saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        _showError('Failed to save wallet');
      }
    } catch (e) {
      _showError('Error saving wallet: $e');
    }
  }

  /// Load a saved wallet
  Future<void> _loadSavedWallet(SavedWallet wallet) async {
    setState(() {
      _privateKeyController.text = wallet.privateKey;
      _currentWallet = wallet;
    });

    // Update last used timestamp
    await WalletStorageService.updateLastUsed(wallet.name);
    await _loadSavedWallets();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Loaded wallet: ${wallet.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Delete a saved wallet
  Future<void> _deleteSavedWallet(SavedWallet wallet) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Wallet'),
            content: Text('Are you sure you want to delete "${wallet.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await WalletStorageService.deleteWallet(wallet.name);
      if (success) {
        await _loadSavedWallets();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deleted wallet: ${wallet.name}'),
            backgroundColor: Colors.orange,
          ),
        );
      } else {
        _showError('Failed to delete wallet');
      }
    }
  }

  /// Generate a random private key for testing purposes
  String _generateRandomPrivateKey() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    final hexString =
        bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    return '0x$hexString';
  }

  Future<void> initPlatformState() async {
    String platformVersion;
    try {
      platformVersion =
          await _toiiXmtpFlutterPlugin.getPlatformVersion() ??
          'Unknown platform version';
    } on PlatformException {
      platformVersion = 'Failed to get platform version.';
    }

    if (!mounted) return;

    setState(() {
      _platformVersion = platformVersion;
    });
  }

  Future<void> _createClient() async {
    if (_privateKeyController.text.isEmpty) {
      _showError('Please enter a private key');
      return;
    }

    setState(() {
      _isLoading = true;
      _status = 'Creating client...';
    });

    try {
      final options = ClientOptions(
        environment: 'dev',
        deviceSyncEnabled: true,
        debugEventsEnabled: true,
      );

      final client = await _toiiXmtpFlutterPlugin.createClient(
        privateKey: _privateKeyController.text,
        options: options,
      );

      setState(() {
        _client = client;
        _status = 'Client created successfully! Inbox ID: ${client.inboxId}';
      });

      // Update current wallet with inbox ID if it exists
      if (_currentWallet != null) {
        await WalletStorageService.updateWalletInboxId(
          _currentWallet!.name,
          client.inboxId,
        );
        await _loadSavedWallets();
      }

      await _loadConversations();
    } catch (e) {
      _showError('Failed to create client: $e');
      print(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadConversations() async {
    if (_client == null) return;

    setState(() {
      _isLoading = true;
      _status = 'Loading conversations...';
    });

    try {
      // Load both conversations and DMs
      final conversations = await _toiiXmtpFlutterPlugin.listConversations();
      final dms = await _toiiXmtpFlutterPlugin.listDms();

      setState(() {
        // Combine conversations and DMs into one list
        _conversations = [...conversations, ...dms];
        _status =
            'Loaded ${conversations.length} conversations and ${dms.length} DMs (Total: ${_conversations.length})';
      });

      // Log DMs for debugging
      print('DMs loaded: ${dms.length}');
      for (final dm in dms) {
        print('DM: ${dm.id} with ${dm.peerInboxId}');
      }
    } catch (e) {
      _showError('Failed to load conversations: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createGroup() async {
    if (_client == null) {
      _showError('Please create a client first');
      return;
    }

    if (_groupNameController.text.isEmpty ||
        _memberInboxIdsController.text.isEmpty) {
      _showError('Please enter group name and member inbox IDs');
      return;
    }

    setState(() {
      _isLoading = true;
      _status = 'Creating group...';
    });

    try {
      final memberInboxIds =
          _memberInboxIdsController.text
              .split(',')
              .map((id) => id.trim())
              .where((id) => id.isNotEmpty)
              .toList();

      final group = await _toiiXmtpFlutterPlugin.createGroup(
        memberInboxIds: memberInboxIds,
        groupName: _groupNameController.text,
        groupDescription: 'Created from Flutter demo app',
      );

      setState(() {
        _status = 'Group created: ${group.name}';
      });

      await _loadConversations();
    } catch (e) {
      _showError('Failed to create group: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createDm() async {
    if (_client == null) {
      _showError('Please create a client first');
      return;
    }

    if (_targetInboxIdController.text.isEmpty) {
      _showError('Please enter target inbox ID');
      return;
    }

    setState(() {
      _isLoading = true;
      _status = 'Creating DM...';
    });

    try {
      final dm = await _toiiXmtpFlutterPlugin.findOrCreateDm(
        _targetInboxIdController.text,
      );

      setState(() {
        _status = 'DM created with ${dm.peerInboxId}. Waiting for sync...';
      });

      // Wait for XMTP network to sync the new DM
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _status = 'Loading conversations after DM creation...';
      });

      await _loadConversations();
    } catch (e) {
      _showError('Failed to create DM: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showError(String message) {
    if (!mounted) return;

    setState(() {
      _status = 'Error: $message';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _copyInboxId() {
    if (_client?.inboxId != null) {
      Clipboard.setData(ClipboardData(text: _client!.inboxId));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Inbox ID copied to clipboard!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _selectConversation(String conversationId) {
    // Find the conversation from the list
    final conversation = _conversations.firstWhere(
      (conv) => conv.id == conversationId,
      orElse: () => throw Exception('Conversation not found'),
    );

    // Check if client is available
    if (_client == null || _client!.inboxId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Client not initialized or missing inbox ID'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Convert to ChatConversation and navigate to chat screen
    final chatConversation = ChatConversation.fromConversation(conversation);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              conversation: chatConversation,
              currentUserInboxId: _client!.inboxId,
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('XMTP v3 Flutter Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Platform info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Platform: $_platformVersion',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Status: $_status',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    if (_client != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Inbox ID: ${_client!.inboxId}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ),
                          IconButton(
                            onPressed: _copyInboxId,
                            icon: const Icon(Icons.copy, size: 20),
                            tooltip: 'Copy Inbox ID',
                            visualDensity: VisualDensity.compact,
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Client creation section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Client Setup',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _privateKeyController,
                      decoration: const InputDecoration(
                        labelText: 'Private Key (hex)',
                        hintText: '0x1111...',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),

                    // Wallet management section
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _walletNameController,
                            decoration: const InputDecoration(
                              labelText: 'Wallet Name (to save)',
                              hintText: 'My Wallet',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isLoading ? null : _saveCurrentWallet,
                          child: const Text('Save'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Saved wallets toggle
                    Row(
                      children: [
                        Text(
                          'Saved Wallets (${_savedWallets.length})',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: () {
                            setState(() {
                              _showSavedWallets = !_showSavedWallets;
                            });
                          },
                          child: Text(_showSavedWallets ? 'Hide' : 'Show'),
                        ),
                      ],
                    ),

                    // Saved wallets list
                    if (_showSavedWallets && _savedWallets.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _savedWallets.length,
                          itemBuilder: (context, index) {
                            final wallet = _savedWallets[index];
                            final isSelected =
                                _currentWallet?.name == wallet.name;

                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor:
                                    isSelected ? Colors.blue : Colors.grey,
                                child: Icon(
                                  Icons.account_balance_wallet,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              title: Text(
                                wallet.name,
                                style: TextStyle(
                                  fontWeight:
                                      isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                ),
                              ),
                              subtitle: Text(
                                'Inbox: ${wallet.displayInboxId}\n'
                                'Created: ${wallet.createdAt.toString().substring(0, 16)}',
                              ),
                              isThreeLine: true,
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    onPressed: () => _loadSavedWallet(wallet),
                                    icon: const Icon(Icons.download, size: 20),
                                    tooltip: 'Load Wallet',
                                  ),
                                  IconButton(
                                    onPressed: () => _deleteSavedWallet(wallet),
                                    icon: const Icon(Icons.delete, size: 20),
                                    tooltip: 'Delete Wallet',
                                    color: Colors.red,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],

                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _createClient,
                      child:
                          _isLoading
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text('Create Client'),
                    ),
                  ],
                ),
              ),
            ),

            if (_client != null) ...[
              const SizedBox(height: 16),

              // Conversation creation section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Create Conversations',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),

                      // Create Group
                      TextField(
                        controller: _groupNameController,
                        decoration: const InputDecoration(
                          labelText: 'Group Name',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _memberInboxIdsController,
                        decoration: const InputDecoration(
                          labelText: 'Member Inbox IDs (comma separated)',
                          hintText: 'inbox1,inbox2,inbox3',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _createGroup,
                        child: const Text('Create Group'),
                      ),

                      const SizedBox(height: 16),

                      // Create DM
                      TextField(
                        controller: _targetInboxIdController,
                        decoration: const InputDecoration(
                          labelText: 'Target Inbox ID for DM',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _createDm,
                        child: const Text('Create DM'),
                      ),
                    ],
                  ),
                ),
              ),

              // Conversations list section
              if (_conversations.isNotEmpty) ...[
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Conversations (${_conversations.length})',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _conversations.length,
                          itemBuilder: (context, index) {
                            final conversation = _conversations[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  child: Icon(
                                    conversation.type == ConversationType.dm
                                        ? Icons.person
                                        : Icons.group,
                                  ),
                                ),
                                title: Text(
                                  conversation.type == ConversationType.dm
                                      ? 'DM with ${(conversation as Dm).peerInboxId.substring(0, 8)}...'
                                      : 'Group: ${conversation.id.substring(0, 8)}...',
                                ),
                                subtitle: Text(
                                  'ID: ${conversation.id}\n'
                                  'Created: ${conversation.createdAt.toString().substring(0, 19)}\n'
                                  'Status: ${conversation.consentState.value}',
                                ),
                                isThreeLine: true,
                                trailing: Icon(
                                  conversation.consentState ==
                                          ConsentState.allowed
                                      ? Icons.check_circle
                                      : Icons.pending,
                                  color:
                                      conversation.consentState ==
                                              ConsentState.allowed
                                          ? Colors.green
                                          : Colors.orange,
                                ),
                                onTap: () {
                                  _selectConversation(conversation.id);
                                },
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
