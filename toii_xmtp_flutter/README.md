# XMTP v3 Flutter Plugin

A comprehensive Flutter plugin for XMTP v3 (Extensible Message Transport Protocol) that enables secure, decentralized messaging in Flutter applications. This plugin provides full support for XMTP v3 features including group chats, direct messages, real-time streaming, and advanced group management.

## Features

### 🔐 **Authentication & Identity**
- Client creation with wallet private keys
- Inbox ID system (XMTP v3's identity system)
- Support for multiple environments (dev, production, local)

### 💬 **Conversations**
- **Direct Messages (DM)**: 1:1 conversations
- **Group Chats**: Multi-user group conversations with advanced management
- Real-time conversation streaming
- Conversation synchronization

### 👥 **Group Management**
- Create and manage group chats
- Add/remove members dynamically
- Admin and Super Admin roles
- Granular permissions system
- Group metadata (name, description, image)
- Disappearing messages support

### 📨 **Messaging**
- Send and receive text messages
- Message history with pagination
- Real-time message streaming
- Optimistic sending (prepare + publish)
- Message reactions support
- Delivery status tracking

### 🔄 **Advanced Features**
- Consent management
- Message compression
- Custom content types
- Sync and state management
- Comprehensive error handling

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  toii_xmtp_flutter: ^0.0.1
```

Then run:

```bash
flutter pub get
```

## Platform Support

- ✅ **Android**: Full XMTP v3 support
- ⏳ **iOS**: Coming soon

## Quick Start

### 1. Initialize XMTP Client

```dart
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

final xmtp = ToiiXmtpFlutter();

// Create client with private key
final client = await xmtp.createClient(
  privateKey: 'your_private_key_hex',
  options: ClientOptions(
    environment: 'dev', // or 'production'
    deviceSyncEnabled: true,
  ),
);

print('Client created! Inbox ID: ${client.inboxId}');
```

### 2. Create a Group Chat

```dart
final group = await xmtp.createGroup(
  memberInboxIds: ['inbox1', 'inbox2', 'inbox3'],
  groupName: 'My Flutter Group',
  groupDescription: 'A group created from Flutter!',
);

print('Group created: ${group.name}');
```

### 3. Send Messages

```dart
// Send a message to the group
final messageId = await xmtp.sendMessage(
  conversationId: group.id,
  content: 'Hello from Flutter! 🚀',
);

print('Message sent: $messageId');
```

### 4. Stream Real-time Messages

```dart
// Listen for new messages in real-time
xmtp.streamMessages(group.id).listen((message) {
  print('New message from ${message.senderInboxId}: ${message.content}');
});
```

### 5. Create Direct Messages

```dart
final dm = await xmtp.findOrCreateDm('target_inbox_id');
await xmtp.sendMessage(
  conversationId: dm.id,
  content: 'Hello in DM!',
);
```

## Advanced Usage

### Group Management

```dart
// Add members to group
await xmtp.addMembers(group.id, ['new_member_inbox_id']);

// Promote member to admin
await xmtp.addAdmin(group.id, 'member_inbox_id');

// Update group metadata
await xmtp.updateGroupName(group.id, 'New Group Name');
await xmtp.updateGroupDescription(group.id, 'Updated description');

// Enable disappearing messages (24 hours)
await xmtp.updateDisappearingMessageSettings(
  group.id,
  DisappearingMessageSettings(
    disappearStartingAt: DateTime.now(),
    retentionDuration: Duration(hours: 24),
  ),
);
```

### Message History

```dart
// Get message history with pagination
final messages = await xmtp.getMessages(
  group.id,
  options: MessageQueryOptions(
    limit: 50,
    direction: SortDirection.descending,
    deliveryStatus: MessageDeliveryStatus.published,
  ),
);

// Get messages with reactions
final messagesWithReactions = await xmtp.getMessagesWithReactions(
  group.id,
  limit: 20,
);
```

### Optimistic Sending

```dart
// Prepare message for optimistic sending
final messageId = await xmtp.prepareMessage(group.id, 'Optimistic message');

// Publish all prepared messages
await xmtp.publishMessages(group.id);
```

## Error Handling

The plugin provides comprehensive error handling with specific exception types:

```dart
try {
  final client = await xmtp.createClient(privateKey: privateKey);
} on ClientCreationException catch (e) {
  print('Failed to create client: ${e.message}');
} on NetworkException catch (e) {
  print('Network error: ${e.message}');
} on XmtpException catch (e) {
  print('XMTP error: ${e.message}');
}
```

## Data Models

### XmtpClient
```dart
class XmtpClient {
  final String inboxId;
  final String installationId;
  final String environment;
  final String dbPath;
  final Map<String, dynamic> publicIdentity;
}
```

### Group
```dart
class Group implements Conversation {
  final String id;
  final String? name;
  final String? description;
  final String? imageUrl;
  final List<String> memberInboxIds;
  final List<String> adminInboxIds;
  final GroupPermissions permissions;
  final DisappearingMessageSettings? disappearingMessageSettings;
  // ... more properties
}
```

### XmtpMessage
```dart
class XmtpMessage {
  final String id;
  final String conversationId;
  final String senderInboxId;
  final String content;
  final DateTime sentAt;
  final MessageDeliveryStatus deliveryStatus;
}
```

## Example App

Check out the comprehensive example app in the `example/` directory that demonstrates:

- Client creation and authentication
- Group chat creation and management
- Direct messaging
- Real-time message streaming
- Error handling
- UI best practices

To run the example:

```bash
cd example
flutter run
```

## Requirements

### Android
- Minimum SDK: 21 (Android 5.0)
- Target SDK: 35
- Kotlin support
- Java 17 compatibility

### Dependencies
The plugin automatically includes all necessary XMTP Android library dependencies.

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- 📖 [XMTP Documentation](https://xmtp.org/docs)
- 🐛 [Report Issues](https://github.com/your-repo/toii_xmtp_flutter/issues)
- 💬 [XMTP Discord](https://discord.gg/xmtp)

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a detailed list of changes and updates.

