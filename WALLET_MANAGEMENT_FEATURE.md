# Tính Năng Quản Lý Ví (Wallet Management Feature)

## Tổng Quan

Đã thêm tính năng lưu trữ và quản lý địa chỉ ví trong ứng dụng Flutter XMTP. Tính năng này cho phép người dùng:

- ✅ Lưu lại private key và thông tin ví sau khi tạo thành công
- ✅ Đặt tên cho từng ví để dễ nhận biết
- ✅ Tải lại ví đã lưu để sử dụng
- ✅ Xóa ví không cần thiết
- ✅ Xem danh sách tất cả ví đã lưu
- ✅ Tự động cập nhật Inbox ID sau khi kết nối thành công

## Các File Đã Thêm/Sửa Đổi

### 1. **Model Classes**

#### `models/saved_wallet.dart`
```dart
class SavedWallet {
  final String name;           // Tên ví do người dùng đặt
  final String privateKey;     // Private key của ví
  final String? inboxId;       // Inbox ID sau khi kết nối XMTP
  final DateTime createdAt;    // Thời gian tạo
  final DateTime? lastUsed;    // Lần sử dụng cuối
}
```

#### `services/wallet_storage_service.dart`
Service quản lý việc lưu trữ ví sử dụng SharedPreferences:
- `getSavedWallets()` - Lấy danh sách ví đã lưu
- `saveWallet()` - Lưu ví mới hoặc cập nhật ví hiện có
- `deleteWallet()` - Xóa ví
- `updateWalletInboxId()` - Cập nhật Inbox ID sau khi kết nối
- `updateLastUsed()` - Cập nhật thời gian sử dụng cuối

### 2. **Dependencies Mới**

Đã thêm vào `pubspec.yaml`:
```yaml
dependencies:
  shared_preferences: ^2.3.2  # Để lưu trữ dữ liệu local
```

### 3. **UI Components Mới**

#### Trong `main.dart`:

**Phần Lưu Ví:**
- TextField để nhập tên ví
- Nút "Save" để lưu ví hiện tại

**Phần Quản Lý Ví:**
- Toggle "Show/Hide" để hiển thị/ẩn danh sách ví
- Danh sách ví đã lưu với thông tin:
  - Tên ví
  - Inbox ID (nếu có)
  - Thời gian tạo
  - Nút "Load" để tải ví
  - Nút "Delete" để xóa ví

## Cách Sử Dụng

### 1. **Lưu Ví Mới**
1. Nhập private key vào trường "Private Key"
2. Nhập tên ví vào trường "Wallet Name"
3. Nhấn nút "Save"
4. Ví sẽ được lưu với thông tin hiện tại

### 2. **Tải Ví Đã Lưu**
1. Nhấn "Show" để hiển thị danh sách ví
2. Nhấn nút "Load" (icon download) bên cạnh ví muốn sử dụng
3. Private key sẽ được tự động điền vào trường nhập

### 3. **Xóa Ví**
1. Hiển thị danh sách ví
2. Nhấn nút "Delete" (icon thùng rác) bên cạnh ví muốn xóa
3. Xác nhận xóa trong dialog

### 4. **Tự Động Cập Nhật Inbox ID**
- Sau khi tạo client thành công, Inbox ID sẽ tự động được cập nhật vào ví hiện tại (nếu có)

## Tính Năng Bảo Mật

### ⚠️ **Lưu Ý Bảo Mật**
- Private keys được lưu trữ trong SharedPreferences (local storage)
- Dữ liệu không được mã hóa trong phiên bản hiện tại
- **Chỉ sử dụng cho mục đích phát triển và testing**
- **KHÔNG sử dụng với private keys thật có giá trị**

### 🔒 **Cải Thiện Bảo Mật Tương Lai**
Có thể thêm các tính năng sau:
- Mã hóa private keys trước khi lưu
- Sử dụng Keychain (iOS) / Keystore (Android)
- Thêm mật khẩu bảo vệ
- Tự động xóa sau thời gian không sử dụng

## Cấu Trúc Dữ Liệu

### SharedPreferences Keys:
- `saved_wallets`: Danh sách ví đã lưu (JSON array)
- `last_used_wallet`: Tên ví sử dụng cuối cùng

### JSON Format:
```json
{
  "name": "My Test Wallet",
  "privateKey": "0x1234567890abcdef...",
  "inboxId": "0xabcdef1234567890...",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "lastUsed": "2024-01-15T14:45:00.000Z"
}
```

## Testing

### Test Cases:
1. **Lưu ví mới** - Kiểm tra ví được lưu thành công
2. **Tải ví đã lưu** - Kiểm tra private key được điền đúng
3. **Xóa ví** - Kiểm tra ví bị xóa khỏi danh sách
4. **Cập nhật Inbox ID** - Kiểm tra ID được cập nhật sau khi kết nối
5. **Persistence** - Kiểm tra dữ liệu vẫn còn sau khi restart app

### Debugging:
- Kiểm tra logs trong console để xem các hoạt động lưu/tải
- Sử dụng Flutter Inspector để kiểm tra state

## Lợi Ích

1. **Tiện Lợi**: Không cần nhập lại private key mỗi lần
2. **Quản Lý Nhiều Ví**: Có thể lưu và chuyển đổi giữa nhiều ví
3. **Theo Dõi**: Xem thông tin kết nối và thời gian sử dụng
4. **Phát Triển**: Tăng tốc quá trình testing với nhiều ví khác nhau

## Hạn Chế Hiện Tại

1. **Bảo Mật**: Private keys không được mã hóa
2. **Backup**: Không có tính năng backup/restore
3. **Sync**: Không đồng bộ giữa các thiết bị
4. **Validation**: Chưa có validation mạnh cho private key format

Tính năng này giúp cải thiện đáng kể trải nghiệm phát triển và testing với XMTP Flutter plugin.
