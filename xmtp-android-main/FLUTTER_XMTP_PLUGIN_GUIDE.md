# Hướng Dẫn Tạo Flutter Plugin cho XMTP V3

## 📋 **Tổng Quan**

Hướng dẫn này sẽ giúp bạn tạo một Flutter plugin để tích hợp thư viện native XMTP V3 Android/iOS, cho phép Flutter app sử dụng các tính năng chat nâng cao của XMTP.

## 🏗️ **Cấu Trúc Plugin**

```
xmtp_flutter/
├── lib/
│   ├── xmtp_flutter.dart              # Main plugin interface
│   ├── src/
│   │   ├── models/                    # Data models
│   │   │   ├── client.dart
│   │   │   ├── conversation.dart
│   │   │   ├── message.dart
│   │   │   └── group.dart
│   │   ├── enums/                     # Enums
│   │   │   ├── conversation_type.dart
│   │   │   ├── consent_state.dart
│   │   │   └── permission_option.dart
│   │   └── exceptions/                # Custom exceptions
│   │       └── xmtp_exceptions.dart
├── android/
│   ├── src/main/kotlin/
│   │   └── com/example/xmtp_flutter/
│   │       ├── XmtpFlutterPlugin.kt   # Main Android plugin
│   │       ├── handlers/              # Method handlers
│   │       │   ├── ClientHandler.kt
│   │       │   ├── ConversationHandler.kt
│   │       │   └── MessageHandler.kt
│   │       └── utils/                 # Utilities
│   │           └── Serializer.kt
│   └── build.gradle                   # Android dependencies
├── ios/
│   ├── Classes/
│   │   ├── XmtpFlutterPlugin.swift    # Main iOS plugin
│   │   ├── Handlers/                  # Method handlers
│   │   └── Utils/                     # Utilities
│   └── xmtp_flutter.podspec           # iOS dependencies
├── example/                           # Example Flutter app
├── pubspec.yaml                       # Flutter dependencies
└── README.md
```

## 🚀 **Bước 1: Tạo Flutter Plugin**

### **1.1 Khởi tạo plugin**
```bash
flutter create --template=plugin --platforms=android,ios xmtp_flutter
cd xmtp_flutter
```

### **1.2 Cập nhật pubspec.yaml**
```yaml
name: xmtp_flutter
description: Flutter plugin for XMTP V3 messaging protocol
version: 1.0.0
homepage: https://github.com/your-username/xmtp_flutter

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  plugin_platform_interface: ^2.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  plugin:
    platforms:
      android:
        package: com.example.xmtp_flutter
        pluginClass: XmtpFlutterPlugin
      ios:
        pluginClass: XmtpFlutterPlugin
```

## 📱 **Bước 2: Cấu Hình Android**

### **2.1 Cập nhật android/build.gradle**
```gradle
android {
    compileSdkVersion 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    
    // XMTP V3 Android SDK
    implementation 'org.xmtp:android:4.0.3'
    
    // Coroutines support
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // JSON serialization
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0'
}
```

### **2.2 Tạo XmtpFlutterPlugin.kt**
```kotlin
package com.example.xmtp_flutter

import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.EventChannel
import kotlinx.coroutines.*
import org.xmtp.android.library.Client
import org.xmtp.android.library.ClientOptions
import com.example.xmtp_flutter.handlers.*

class XmtpFlutterPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var methodChannel: MethodChannel
    private lateinit var eventChannel: EventChannel
    private val mainScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // Handlers
    private lateinit var clientHandler: ClientHandler
    private lateinit var conversationHandler: ConversationHandler
    private lateinit var messageHandler: MessageHandler
    
    override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "xmtp_flutter")
        methodChannel.setMethodCallHandler(this)
        
        eventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "xmtp_flutter_events")
        
        // Initialize handlers
        clientHandler = ClientHandler()
        conversationHandler = ConversationHandler()
        messageHandler = MessageHandler()
    }

    override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
        when (call.method) {
            // Client methods
            "createClient" -> clientHandler.createClient(call, result, mainScope)
            "getInboxId" -> clientHandler.getInboxId(call, result)
            
            // Conversation methods
            "listConversations" -> conversationHandler.listConversations(call, result, mainScope)
            "createGroup" -> conversationHandler.createGroup(call, result, mainScope)
            "findOrCreateDm" -> conversationHandler.findOrCreateDm(call, result, mainScope)
            
            // Message methods
            "sendMessage" -> messageHandler.sendMessage(call, result, mainScope)
            "getMessages" -> messageHandler.getMessages(call, result, mainScope)
            "streamMessages" -> messageHandler.streamMessages(call, result, eventChannel)
            
            else -> result.notImplemented()
        }
    }

    override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel.setMethodCallHandler(null)
        mainScope.cancel()
    }
}
```

### **2.3 Tạo ClientHandler.kt**
```kotlin
package com.example.xmtp_flutter.handlers

import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.*
import org.xmtp.android.library.Client
import org.xmtp.android.library.ClientOptions
import org.xmtp.android.library.XMTPEnvironment

class ClientHandler {
    private var client: Client? = null

    fun createClient(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val privateKeyHex = call.argument<String>("privateKey")
                    ?: throw IllegalArgumentException("Private key is required")
                
                val environment = call.argument<String>("environment") ?: "dev"
                val xmtpEnv = when (environment) {
                    "production" -> XMTPEnvironment.PRODUCTION
                    "dev" -> XMTPEnvironment.DEV
                    else -> XMTPEnvironment.DEV
                }

                val options = ClientOptions(
                    api = ClientOptions.Api(
                        env = xmtpEnv,
                        isSecure = true
                    )
                )

                // Create client from private key
                client = Client.create(
                    account = PrivateKeyBuilder(privateKeyHex),
                    options = options
                )

                val clientData = mapOf(
                    "inboxId" to client!!.inboxId,
                    "address" to client!!.address
                )

                withContext(Dispatchers.Main) {
                    result.success(clientData)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    result.error("CREATE_CLIENT_ERROR", e.message, e.stackTraceToString())
                }
            }
        }
    }

    fun getInboxId(call: MethodCall, result: Result) {
        val inboxId = client?.inboxId
        if (inboxId != null) {
            result.success(inboxId)
        } else {
            result.error("NO_CLIENT", "Client not initialized", null)
        }
    }

    fun getClient(): Client? = client
}
```

### **2.4 Tạo ConversationHandler.kt**
```kotlin
package com.example.xmtp_flutter.handlers

import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.*
import org.xmtp.android.library.*
import com.example.xmtp_flutter.utils.Serializer

class ConversationHandler {
    
    fun listConversations(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = ClientManager.getClient()
                    ?: throw IllegalStateException("Client not initialized")

                client.conversations.sync()
                
                val groups = client.conversations.listGroups()
                val dms = client.conversations.listDms()
                
                val conversations = mutableListOf<Map<String, Any>>()
                
                // Add groups
                groups.forEach { group ->
                    conversations.add(Serializer.groupToMap(group))
                }
                
                // Add DMs
                dms.forEach { dm ->
                    conversations.add(Serializer.dmToMap(dm))
                }

                withContext(Dispatchers.Main) {
                    result.success(conversations)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    result.error("LIST_CONVERSATIONS_ERROR", e.message, e.stackTraceToString())
                }
            }
        }
    }

    fun createGroup(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = ClientManager.getClient()
                    ?: throw IllegalStateException("Client not initialized")

                val memberInboxIds = call.argument<List<String>>("memberInboxIds")
                    ?: throw IllegalArgumentException("Member inbox IDs required")
                
                val groupName = call.argument<String>("groupName")
                val groupDescription = call.argument<String>("groupDescription")
                val groupImageUrl = call.argument<String>("groupImageUrl")

                val group = client.conversations.newGroup(
                    accountAddresses = memberInboxIds,
                    permissions = GroupPermissions.GroupCreatorIsAdmin(),
                    groupName = groupName,
                    groupImageUrlSquare = groupImageUrl,
                    groupDescription = groupDescription
                )

                withContext(Dispatchers.Main) {
                    result.success(Serializer.groupToMap(group))
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    result.error("CREATE_GROUP_ERROR", e.message, e.stackTraceToString())
                }
            }
        }
    }

    fun findOrCreateDm(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = ClientManager.getClient()
                    ?: throw IllegalStateException("Client not initialized")

                val targetInboxId = call.argument<String>("targetInboxId")
                    ?: throw IllegalArgumentException("Target inbox ID required")

                val dm = client.conversations.findOrCreateDm(targetInboxId)

                withContext(Dispatchers.Main) {
                    result.success(Serializer.dmToMap(dm))
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    result.error("FIND_OR_CREATE_DM_ERROR", e.message, e.stackTraceToString())
                }
            }
        }
    }
}
```

## 🍎 **Bước 3: Cấu Hình iOS**

### **3.1 Cập nhật ios/xmtp_flutter.podspec**
```ruby
Pod::Spec.new do |s|
  s.name             = 'xmtp_flutter'
  s.version          = '1.0.0'
  s.summary          = 'Flutter plugin for XMTP V3'
  s.description      = 'A Flutter plugin that provides XMTP V3 messaging capabilities'
  s.homepage         = 'https://github.com/your-username/xmtp_flutter'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Name' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.dependency 'XMTP', '~> 3.0.0'
  s.platform = :ios, '14.0'
  s.swift_version = '5.0'
end
```

### **3.2 Tạo XmtpFlutterPlugin.swift**
```swift
import Flutter
import UIKit
import XMTP

public class XmtpFlutterPlugin: NSObject, FlutterPlugin {
    private var client: XMTP.Client?
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "xmtp_flutter", binaryMessenger: registrar.messenger())
        let instance = XmtpFlutterPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }

    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "createClient":
            createClient(call: call, result: result)
        case "getInboxId":
            getInboxId(result: result)
        case "listConversations":
            listConversations(result: result)
        case "createGroup":
            createGroup(call: call, result: result)
        case "findOrCreateDm":
            findOrCreateDm(call: call, result: result)
        case "sendMessage":
            sendMessage(call: call, result: result)
        case "getMessages":
            getMessages(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func createClient(call: FlutterMethodCall, result: @escaping FlutterResult) {
        Task {
            do {
                guard let args = call.arguments as? [String: Any],
                      let privateKeyHex = args["privateKey"] as? String else {
                    result(FlutterError(code: "INVALID_ARGUMENTS", message: "Private key required", details: nil))
                    return
                }
                
                let environment = args["environment"] as? String ?? "dev"
                let options = ClientOptions(
                    api: .init(env: environment == "production" ? .production : .dev, isSecure: true)
                )
                
                self.client = try await Client.create(
                    account: try PrivateKey(privateKeyHex),
                    options: options
                )
                
                let clientData: [String: Any] = [
                    "inboxId": self.client!.inboxID,
                    "address": self.client!.address
                ]
                
                DispatchQueue.main.async {
                    result(clientData)
                }
            } catch {
                DispatchQueue.main.async {
                    result(FlutterError(code: "CREATE_CLIENT_ERROR", message: error.localizedDescription, details: nil))
                }
            }
        }
    }
    
    // Implement other methods...
}
```

## 📦 **Bước 4: Dart Interface**

### **4.1 Tạo lib/xmtp_flutter.dart**
```dart
library xmtp_flutter;

import 'dart:async';
import 'package:flutter/services.dart';

// Export models
export 'src/models/client.dart';
export 'src/models/conversation.dart';
export 'src/models/message.dart';
export 'src/models/group.dart';
export 'src/enums/conversation_type.dart';
export 'src/enums/consent_state.dart';
export 'src/enums/permission_option.dart';

class XmtpFlutter {
  static const MethodChannel _channel = MethodChannel('xmtp_flutter');
  static const EventChannel _eventChannel = EventChannel('xmtp_flutter_events');

  /// Create XMTP client
  static Future<XmtpClient> createClient({
    required String privateKey,
    String environment = 'dev',
  }) async {
    try {
      final result = await _channel.invokeMethod('createClient', {
        'privateKey': privateKey,
        'environment': environment,
      });
      
      return XmtpClient.fromMap(Map<String, dynamic>.from(result));
    } catch (e) {
      throw XmtpException('Failed to create client: $e');
    }
  }

  /// Get inbox ID
  static Future<String> getInboxId() async {
    try {
      final result = await _channel.invokeMethod('getInboxId');
      return result as String;
    } catch (e) {
      throw XmtpException('Failed to get inbox ID: $e');
    }
  }

  /// List all conversations
  static Future<List<Conversation>> listConversations() async {
    try {
      final result = await _channel.invokeMethod('listConversations');
      final List<dynamic> conversations = result as List<dynamic>;
      
      return conversations.map((conv) => 
        Conversation.fromMap(Map<String, dynamic>.from(conv))
      ).toList();
    } catch (e) {
      throw XmtpException('Failed to list conversations: $e');
    }
  }

  /// Create a new group
  static Future<Group> createGroup({
    required List<String> memberInboxIds,
    String? groupName,
    String? groupDescription,
    String? groupImageUrl,
  }) async {
    try {
      final result = await _channel.invokeMethod('createGroup', {
        'memberInboxIds': memberInboxIds,
        'groupName': groupName,
        'groupDescription': groupDescription,
        'groupImageUrl': groupImageUrl,
      });
      
      return Group.fromMap(Map<String, dynamic>.from(result));
    } catch (e) {
      throw XmtpException('Failed to create group: $e');
    }
  }

  /// Find or create DM conversation
  static Future<Dm> findOrCreateDm(String targetInboxId) async {
    try {
      final result = await _channel.invokeMethod('findOrCreateDm', {
        'targetInboxId': targetInboxId,
      });
      
      return Dm.fromMap(Map<String, dynamic>.from(result));
    } catch (e) {
      throw XmtpException('Failed to find or create DM: $e');
    }
  }

  /// Send message
  static Future<String> sendMessage({
    required String conversationId,
    required String content,
    String? contentType,
  }) async {
    try {
      final result = await _channel.invokeMethod('sendMessage', {
        'conversationId': conversationId,
        'content': content,
        'contentType': contentType ?? 'text/plain',
      });
      
      return result as String;
    } catch (e) {
      throw XmtpException('Failed to send message: $e');
    }
  }

  /// Get messages from conversation
  static Future<List<Message>> getMessages({
    required String conversationId,
    int? limit,
    int? beforeNs,
    int? afterNs,
  }) async {
    try {
      final result = await _channel.invokeMethod('getMessages', {
        'conversationId': conversationId,
        'limit': limit,
        'beforeNs': beforeNs,
        'afterNs': afterNs,
      });
      
      final List<dynamic> messages = result as List<dynamic>;
      return messages.map((msg) => 
        Message.fromMap(Map<String, dynamic>.from(msg))
      ).toList();
    } catch (e) {
      throw XmtpException('Failed to get messages: $e');
    }
  }

  /// Stream messages from conversation
  static Stream<Message> streamMessages(String conversationId) {
    return _eventChannel.receiveBroadcastStream({
      'method': 'streamMessages',
      'conversationId': conversationId,
    }).map((data) => Message.fromMap(Map<String, dynamic>.from(data)));
  }
}

/// XMTP Exception
class XmtpException implements Exception {
  final String message;
  XmtpException(this.message);
  
  @override
  String toString() => 'XmtpException: $message';
}
```

### **4.2 Tạo Models**

#### **lib/src/models/client.dart**
```dart
class XmtpClient {
  final String inboxId;
  final String address;

  XmtpClient({
    required this.inboxId,
    required this.address,
  });

  factory XmtpClient.fromMap(Map<String, dynamic> map) {
    return XmtpClient(
      inboxId: map['inboxId'] as String,
      address: map['address'] as String,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'inboxId': inboxId,
      'address': address,
    };
  }
}
```

#### **lib/src/models/conversation.dart**
```dart
import 'conversation_type.dart';
import 'consent_state.dart';

abstract class Conversation {
  final String id;
  final ConversationType type;
  final DateTime createdAt;
  final ConsentState consentState;

  Conversation({
    required this.id,
    required this.type,
    required this.createdAt,
    required this.consentState,
  });

  factory Conversation.fromMap(Map<String, dynamic> map) {
    final type = ConversationType.values.firstWhere(
      (e) => e.toString().split('.').last == map['type'],
    );

    switch (type) {
      case ConversationType.group:
        return Group.fromMap(map);
      case ConversationType.dm:
        return Dm.fromMap(map);
    }
  }
}
```

#### **lib/src/models/group.dart**
```dart
import 'conversation.dart';
import '../enums/conversation_type.dart';
import '../enums/consent_state.dart';

class Group extends Conversation {
  final String? name;
  final String? description;
  final String? imageUrl;
  final List<String> memberInboxIds;
  final List<String> adminInboxIds;
  final List<String> superAdminInboxIds;
  final bool isCreator;

  Group({
    required String id,
    required DateTime createdAt,
    required ConsentState consentState,
    this.name,
    this.description,
    this.imageUrl,
    required this.memberInboxIds,
    required this.adminInboxIds,
    required this.superAdminInboxIds,
    required this.isCreator,
  }) : super(
          id: id,
          type: ConversationType.group,
          createdAt: createdAt,
          consentState: consentState,
        );

  factory Group.fromMap(Map<String, dynamic> map) {
    return Group(
      id: map['id'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int),
      consentState: ConsentState.values.firstWhere(
        (e) => e.toString().split('.').last == map['consentState'],
      ),
      name: map['name'] as String?,
      description: map['description'] as String?,
      imageUrl: map['imageUrl'] as String?,
      memberInboxIds: List<String>.from(map['memberInboxIds'] as List),
      adminInboxIds: List<String>.from(map['adminInboxIds'] as List),
      superAdminInboxIds: List<String>.from(map['superAdminInboxIds'] as List),
      isCreator: map['isCreator'] as bool,
    );
  }
}
```

#### **lib/src/models/dm.dart**
```dart
import 'conversation.dart';
import '../enums/conversation_type.dart';
import '../enums/consent_state.dart';

class Dm extends Conversation {
  final String peerInboxId;

  Dm({
    required String id,
    required DateTime createdAt,
    required ConsentState consentState,
    required this.peerInboxId,
  }) : super(
          id: id,
          type: ConversationType.dm,
          createdAt: createdAt,
          consentState: consentState,
        );

  factory Dm.fromMap(Map<String, dynamic> map) {
    return Dm(
      id: map['id'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int),
      consentState: ConsentState.values.firstWhere(
        (e) => e.toString().split('.').last == map['consentState'],
      ),
      peerInboxId: map['peerInboxId'] as String,
    );
  }
}
```

#### **lib/src/models/message.dart**
```dart
class Message {
  final String id;
  final String conversationId;
  final String senderInboxId;
  final String content;
  final String contentType;
  final DateTime sentAt;
  final Map<String, dynamic>? metadata;

  Message({
    required this.id,
    required this.conversationId,
    required this.senderInboxId,
    required this.content,
    required this.contentType,
    required this.sentAt,
    this.metadata,
  });

  factory Message.fromMap(Map<String, dynamic> map) {
    return Message(
      id: map['id'] as String,
      conversationId: map['conversationId'] as String,
      senderInboxId: map['senderInboxId'] as String,
      content: map['content'] as String,
      contentType: map['contentType'] as String,
      sentAt: DateTime.fromMillisecondsSinceEpoch(map['sentAt'] as int),
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'conversationId': conversationId,
      'senderInboxId': senderInboxId,
      'content': content,
      'contentType': contentType,
      'sentAt': sentAt.millisecondsSinceEpoch,
      'metadata': metadata,
    };
  }
}
```

### **4.3 Tạo Enums**

#### **lib/src/enums/conversation_type.dart**
```dart
enum ConversationType {
  group,
  dm,
}
```

#### **lib/src/enums/consent_state.dart**
```dart
enum ConsentState {
  unknown,
  allowed,
  denied,
}
```

#### **lib/src/enums/permission_option.dart**
```dart
enum PermissionOption {
  allow,
  deny,
  adminOnly,
  superAdminOnly,
}
```

## 📝 **Bước 5: Example App**

### **5.1 Tạo example/lib/main.dart**
```dart
import 'package:flutter/material.dart';
import 'package:xmtp_flutter/xmtp_flutter.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'XMTP Flutter Demo',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const XmtpDemo(),
    );
  }
}

class XmtpDemo extends StatefulWidget {
  const XmtpDemo({Key? key}) : super(key: key);

  @override
  State<XmtpDemo> createState() => _XmtpDemoState();
}

class _XmtpDemoState extends State<XmtpDemo> {
  XmtpClient? _client;
  List<Conversation> _conversations = [];
  final _privateKeyController = TextEditingController();
  final _messageController = TextEditingController();
  String? _selectedConversationId;
  List<Message> _messages = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('XMTP Flutter Demo')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Client section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Client', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _privateKeyController,
                      decoration: const InputDecoration(
                        labelText: 'Private Key (Hex)',
                        hintText: 'Enter your private key...',
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _createClient,
                          child: const Text('Create Client'),
                        ),
                        const SizedBox(width: 8),
                        if (_client != null)
                          Expanded(
                            child: Text(
                              'Inbox ID: ${_client!.inboxId}',
                              style: const TextStyle(fontSize: 12),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Conversations section
            if (_client != null) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Conversations', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                          ElevatedButton(
                            onPressed: _loadConversations,
                            child: const Text('Refresh'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 200,
                        child: ListView.builder(
                          itemCount: _conversations.length,
                          itemBuilder: (context, index) {
                            final conversation = _conversations[index];
                            return ListTile(
                              title: Text(_getConversationTitle(conversation)),
                              subtitle: Text('Type: ${conversation.type.toString().split('.').last}'),
                              selected: _selectedConversationId == conversation.id,
                              onTap: () => _selectConversation(conversation.id),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Messages section
              if (_selectedConversationId != null) ...[
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Messages', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                            ElevatedButton(
                              onPressed: _loadMessages,
                              child: const Text('Refresh'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          height: 200,
                          child: ListView.builder(
                            itemCount: _messages.length,
                            itemBuilder: (context, index) {
                              final message = _messages[index];
                              return ListTile(
                                title: Text(message.content),
                                subtitle: Text('From: ${message.senderInboxId}'),
                                dense: true,
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _messageController,
                                decoration: const InputDecoration(
                                  labelText: 'Message',
                                  hintText: 'Type your message...',
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: _sendMessage,
                              child: const Text('Send'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _createClient() async {
    try {
      final privateKey = _privateKeyController.text.trim();
      if (privateKey.isEmpty) {
        _showSnackBar('Please enter a private key');
        return;
      }

      final client = await XmtpFlutter.createClient(
        privateKey: privateKey,
        environment: 'dev',
      );

      setState(() {
        _client = client;
      });

      _showSnackBar('Client created successfully!');
    } catch (e) {
      _showSnackBar('Error creating client: $e');
    }
  }

  Future<void> _loadConversations() async {
    try {
      final conversations = await XmtpFlutter.listConversations();
      setState(() {
        _conversations = conversations;
      });
    } catch (e) {
      _showSnackBar('Error loading conversations: $e');
    }
  }

  void _selectConversation(String conversationId) {
    setState(() {
      _selectedConversationId = conversationId;
      _messages.clear();
    });
    _loadMessages();
  }

  Future<void> _loadMessages() async {
    if (_selectedConversationId == null) return;

    try {
      final messages = await XmtpFlutter.getMessages(
        conversationId: _selectedConversationId!,
        limit: 50,
      );
      setState(() {
        _messages = messages;
      });
    } catch (e) {
      _showSnackBar('Error loading messages: $e');
    }
  }

  Future<void> _sendMessage() async {
    if (_selectedConversationId == null || _messageController.text.trim().isEmpty) {
      return;
    }

    try {
      await XmtpFlutter.sendMessage(
        conversationId: _selectedConversationId!,
        content: _messageController.text.trim(),
      );

      _messageController.clear();
      _loadMessages(); // Refresh messages
    } catch (e) {
      _showSnackBar('Error sending message: $e');
    }
  }

  String _getConversationTitle(Conversation conversation) {
    if (conversation is Group) {
      return conversation.name ?? 'Unnamed Group';
    } else if (conversation is Dm) {
      return 'DM with ${conversation.peerInboxId}';
    }
    return 'Unknown Conversation';
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  void dispose() {
    _privateKeyController.dispose();
    _messageController.dispose();
    super.dispose();
  }
}
```

## 🧪 **Bước 6: Testing**

### **6.1 Tạo test/xmtp_flutter_test.dart**
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:xmtp_flutter/xmtp_flutter.dart';
import 'package:flutter/services.dart';

void main() {
  const MethodChannel channel = MethodChannel('xmtp_flutter');

  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    channel.setMockMethodCallHandler((MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'createClient':
          return {
            'inboxId': 'test_inbox_id',
            'address': '******************************************',
          };
        case 'getInboxId':
          return 'test_inbox_id';
        case 'listConversations':
          return [];
        default:
          return null;
      }
    });
  });

  tearDown(() {
    channel.setMockMethodCallHandler(null);
  });

  test('createClient returns valid client', () async {
    final client = await XmtpFlutter.createClient(
      privateKey: 'test_private_key',
      environment: 'dev',
    );

    expect(client.inboxId, 'test_inbox_id');
    expect(client.address, '******************************************');
  });

  test('getInboxId returns inbox ID', () async {
    final inboxId = await XmtpFlutter.getInboxId();
    expect(inboxId, 'test_inbox_id');
  });

  test('listConversations returns empty list', () async {
    final conversations = await XmtpFlutter.listConversations();
    expect(conversations, isEmpty);
  });
}
```

## 📚 **Bước 7: Documentation & Deployment**

### **7.1 Cập nhật README.md**
```markdown
# XMTP Flutter Plugin

A Flutter plugin that provides XMTP V3 messaging capabilities for both Android and iOS.

## Features

- ✅ XMTP V3 Client creation and management
- ✅ Direct Messages (DM) support
- ✅ Group chat functionality
- ✅ Real-time message streaming
- ✅ Message history retrieval
- ✅ Group management (add/remove members, admin roles)
- ✅ Disappearing messages
- ✅ Cross-platform support (Android/iOS)

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  xmtp_flutter: ^1.0.0
```

## Usage

### Initialize Client

```dart
import 'package:xmtp_flutter/xmtp_flutter.dart';

// Create client with private key
final client = await XmtpFlutter.createClient(
  privateKey: 'your_private_key_hex',
  environment: 'dev', // or 'production'
);
```

### Create Group Chat

```dart
final group = await XmtpFlutter.createGroup(
  memberInboxIds: ['inbox1', 'inbox2', 'inbox3'],
  groupName: 'My Group',
  groupDescription: 'Group description',
);
```

### Send Messages

```dart
await XmtpFlutter.sendMessage(
  conversationId: group.id,
  content: 'Hello, group!',
);
```

### Stream Messages

```dart
XmtpFlutter.streamMessages(conversationId).listen((message) {
  print('New message: ${message.content}');
});
```

## Platform Requirements

### Android
- Minimum SDK: 21
- Target SDK: 34
- Kotlin support

### iOS
- Minimum iOS version: 14.0
- Swift 5.0+

## License

MIT License
```

### **7.2 Publish to pub.dev**
```bash
# Validate package
flutter packages pub publish --dry-run

# Publish package
flutter packages pub publish
```

## 🎯 **Tóm Tắt**

Plugin Flutter này cung cấp:

1. **✅ Complete XMTP V3 Integration**: Tích hợp đầy đủ các tính năng XMTP V3
2. **✅ Cross-platform Support**: Hỗ trợ cả Android và iOS
3. **✅ Real-time Messaging**: Stream messages real-time
4. **✅ Group Management**: Tạo và quản lý group chat
5. **✅ Type-safe Dart API**: API Dart type-safe và dễ sử dụng
6. **✅ Example App**: Ứng dụng demo đầy đủ tính năng
7. **✅ Testing Support**: Unit tests và integration tests
8. **✅ Documentation**: Tài liệu chi tiết và examples

Plugin này cho phép Flutter developers dễ dàng tích hợp XMTP V3 vào ứng dụng của họ với minimal setup và maximum functionality.