# XMTP V3 Android SDK - <PERSON><PERSON><PERSON> Chi Tiết

## 🔐 **Authentication & Identity**
```kotlin
// Tạo client với wallet
val client = Client.create(account = wallet, options = options)

// Inbox ID system (thay thế address)
val inboxId = client.inboxId
val inboxIdFromAddress = client.inboxIdFromIdentity(identity)
```

## 💬 **Conversations (Cuộc trò chuyện)**

### **1. Direct Messages (DM) - 1:1**
```kotlin
// Tạo hoặc tìm DM conversation
val dm = client.conversations.findOrCreateDm(targetInboxId)
val conversation = Conversation.Dm(dm)

// G<PERSON>i tin nhắn
dm.send("Hello!")
dm.send(content, options)
```

### **2. Groups - Nhóm chat**
```kotlin
// Tạo group
val group = client.conversations.newGroup(memberInboxIds, permissions, metadata)

// Thêm/xóa members
group.addMembers(listOf(inboxId))
group.removeMembers(listOf(inboxId))

// Quản lý admin
group.addAdmin(inboxId)
group.removeAdmin(inboxId)
group.addSuperAdmin(inboxId)
```

## 📨 **Messaging Features**

### **Message Types**
```kotlin
// Text message
group.send("Hello world")

// Custom content với codec
group.send(customContent, SendOptions(contentType = ContentTypeText))

// Với compression
group.send(content, SendOptions(compression = Compression.GZIP))
```

### **Message Management**
```kotlin
// Lấy messages với filters
val messages = group.messages(
    limit = 50,
    beforeNs = timestamp,
    afterNs = timestamp,
    direction = SortDirection.DESCENDING,
    deliveryStatus = MessageDeliveryStatus.PUBLISHED
)

// Messages với reactions
val messagesWithReactions = group.messagesWithReactions(limit = 50)

// Optimistic sending (gửi trước, publish sau)
val messageId = group.prepareMessage(content)
group.publishMessages()
```

### **Real-time Streaming**
```kotlin
// Stream messages real-time
group.streamMessages().collect { message ->
    // Handle new message
}

// Stream conversations
client.conversations.streamGroups().collect { group ->
    // Handle new group
}
```

## 👥 **Group Management**

### **Metadata**
```kotlin
// Cập nhật thông tin group
group.updateName("New Group Name")
group.updateDescription("Group description")
group.updateImageUrl("https://image.url")

// Đọc thông tin
val name = group.name
val description = group.description
val imageUrl = group.imageUrl
val createdAt = group.createdAt
```

### **Permissions & Roles**
```kotlin
// Kiểm tra quyền
val isAdmin = group.isAdmin(inboxId)
val isSuperAdmin = group.isSuperAdmin(inboxId)
val isCreator = group.isCreator()

// Cập nhật permissions
group.updateAddMemberPermission(PermissionOption.ADMIN_ONLY)
group.updateRemoveMemberPermission(PermissionOption.SUPER_ADMIN_ONLY)
group.updateNamePermission(PermissionOption.ALLOW)

// Lấy danh sách
val admins = group.listAdmins()
val superAdmins = group.listSuperAdmins()
val members = group.members()
```

## ⏰ **Disappearing Messages**
```kotlin
// Bật disappearing messages
val settings = DisappearingMessageSettings(
    disappearStartingAtNs = System.currentTimeMillis() * 1_000_000,
    retentionDurationInNs = 24 * 60 * 60 * 1_000_000_000L // 24 hours
)
group.updateDisappearingMessageSettings(settings)

// Tắt disappearing messages
group.clearDisappearingMessageSettings()

// Kiểm tra trạng thái
val isEnabled = group.isDisappearingMessagesEnabled
val currentSettings = group.disappearingMessageSettings
```

## 🔄 **Sync & State Management**
```kotlin
// Sync conversations
client.conversations.sync()
group.sync()

// Consent management
group.updateConsentState(ConsentState.ALLOWED)
val consentState = group.consentState()

// Check active status
val isActive = group.isActive()
```

## 🎨 **Content Codecs**
```kotlin
// Text codec (built-in)
ContentTypeText

// Custom codecs có thể register
Client.codecRegistry.register(customCodec)

// Fallback content
encoded.fallback = "Fallback text for unsupported clients"
```

## 🔍 **Debug & Monitoring**
```kotlin
// Debug information
val debugInfo = group.getDebugInformation()
val forkStatus = group.commitLogForkStatus()

// Push notifications
val pushTopics = group.getPushTopics()
val hmacKeys = group.getHmacKeys()
```

## 📊 **Advanced Features**

### **Delivery Status**
```kotlin
enum class MessageDeliveryStatus {
    ALL, PUBLISHED, UNPUBLISHED, FAILED
}
```

### **Member Management**
```kotlin
// Thêm member bằng identity
group.addMembersByIdentity(listOf(publicIdentity))

// Thêm bằng inbox ID
group.addMembers(listOf(inboxId))

// Lấy peer inbox IDs (loại trừ current user)
val peerIds = group.peerInboxIds()
```

### **Message Processing**
```kotlin
// Process streamed message
val decodedMessage = group.processMessage(messageBytes)
```

## 🔧 **Practical Examples**

### **Tạo và quản lý Group Chat**
```kotlin
// 1. Tạo group mới
val members = listOf("inbox1", "inbox2", "inbox3")
val group = client.conversations.newGroup(
    accountAddresses = members,
    permissions = GroupPermissions(
        policyType = GroupPermissions.PolicyType.ADMIN_ONLY,
        policySet = PermissionPolicySet()
    ),
    groupName = "Team Chat",
    groupImageUrlSquare = "https://example.com/avatar.png",
    groupDescription = "Our team discussion group"
)

// 2. Gửi tin nhắn welcome
group.send("Welcome to our team chat! 🎉")

// 3. Stream messages real-time
group.streamMessages().collect { message ->
    println("New message from ${message.senderInboxId}: ${message.body}")
}

// 4. Thêm member mới
group.addMembers(listOf("newMemberInboxId"))

// 5. Cập nhật permissions
group.updateAddMemberPermission(PermissionOption.ADMIN_ONLY)
```

### **Direct Messages**
```kotlin
// Tạo/tìm DM conversation
val dm = client.conversations.findOrCreateDm("targetInboxId")

// Gửi tin nhắn
dm.send("Hello! How are you?")

// Stream DM messages
dm.streamMessages().collect { message ->
    // Handle direct message
}
```

### **Disappearing Messages Setup**
```kotlin
// Thiết lập tin nhắn tự xóa sau 1 giờ
val settings = DisappearingMessageSettings(
    disappearStartingAtNs = System.currentTimeMillis() * 1_000_000,
    retentionDurationInNs = 3600 * 1_000_000_000L // 1 hour
)
group.updateDisappearingMessageSettings(settings)

// Kiểm tra và gửi tin nhắn
if (group.isDisappearingMessagesEnabled) {
    group.send("This message will disappear in 1 hour")
}
```

## 🆚 **So sánh V2 vs V3**

| Feature | V2 | V3 |
|---------|----|----|
| **Identity** | Wallet Address | Inbox ID |
| **Groups** | ❌ | ✅ Multi-user groups |
| **DMs** | ✅ | ✅ Improved DMs |
| **Admin Roles** | ❌ | ✅ Admin/SuperAdmin |
| **Disappearing Messages** | ❌ | ✅ |
| **Permissions** | ❌ | ✅ Granular permissions |
| **MLS Security** | ❌ | ✅ |
| **Real-time Sync** | Limited | ✅ Enhanced |
| **Group Metadata** | ❌ | ✅ Name, description, image |
| **Member Management** | ❌ | ✅ Add/remove dynamically |
| **Message Reactions** | ❌ | ✅ Built-in support |
| **Optimistic Sending** | ❌ | ✅ Better UX |

## 🚀 **Migration từ V2 lên V3**

### **Key Changes:**
1. **Address → Inbox ID**: Thay đổi từ wallet address sang inbox ID system
2. **Group Support**: V3 hỗ trợ đầy đủ group chat với quản lý permissions
3. **Enhanced Security**: MLS (Message Layer Security) protocol
4. **Better UX**: Optimistic sending, real-time streaming
5. **Rich Metadata**: Group names, descriptions, images

### **Breaking Changes:**
- API methods đã thay đổi
- Identity system mới
- Group permissions system
- Message format updates

---

**XMTP V3 là bản nâng cấp lớn với nhiều tính năng enterprise-ready, phù hợp cho các ứng dụng chat phức tạp với group management, security cao và user experience tốt hơn.**