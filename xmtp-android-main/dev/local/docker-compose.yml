services:
  node:
    image: xmtp/node-go:latest
    platform: linux/amd64
    environment:
      - GOWAKU-NODEKEY=8a30dcb604b0b53627a5adc054dbf434b446628d4bd1eccc681d223f0550ce67
    command:
      - --store.enable
      - --store.db-connection-string=********************************/postgres?sslmode=disable
      - --store.reader-db-connection-string=********************************/postgres?sslmode=disable
      - --mls-store.db-connection-string=***********************************/postgres?sslmode=disable
      - --mls-validation.grpc-address=validation:50051
      - --api.enable-mls
      - --wait-for-db=30s
      - --api.authn.enable
    ports:
      - 5555:5555
      - 5556:5556
    depends_on:
      - db

  validation:
    image: ghcr.io/xmtp/mls-validation-service:main
    platform: linux/amd64
    environment:
      ANVIL_URL: "http://anvil:8545"

  anvil:
    image: ghcr.io/foundry-rs/foundry
    platform: linux/amd64
    entrypoint: ["anvil", "--host", "0.0.0.0"]
    working_dir: /anvil
    ports:
      - 8545:8545

  db:
    image: postgres:13
    environment:
      POSTGRES_PASSWORD: xmtp

  mlsdb:
    image: postgres:13
    environment:
      POSTGRES_PASSWORD: xmtp
