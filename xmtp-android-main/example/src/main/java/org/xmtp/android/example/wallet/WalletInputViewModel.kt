package org.xmtp.android.example.wallet

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.xmtp.android.example.ClientManager
import org.xmtp.android.library.Client
import org.xmtp.android.library.Conversation
import org.xmtp.android.library.libxmtp.IdentityKind
import org.xmtp.android.library.libxmtp.PublicIdentity

class WalletInputViewModel : ViewModel() {

    private val _uiState = MutableStateFlow<UiState>(UiState.Idle)
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()

    fun validateAndConnect(wallet1: String, wallet2: String) {
        viewModelScope.launch {
            _uiState.value = UiState.Loading
            
            try {
                // Validate wallet addresses
                val validatedWallet1 = validateWalletAddress(wallet1)
                val validatedWallet2 = validateWalletAddress(wallet2)
                
                if (validatedWallet1 == null || validatedWallet2 == null) {
                    _uiState.value = UiState.Error("Địa chỉ wallet không hợp lệ. Vui lòng nhập địa chỉ Ethereum đúng định dạng (0x + 40 ký tự hex)")
                    return@launch
                }
                
                // Create conversation between the two wallets
                val conversation = createConversationBetweenWallets(validatedWallet1, validatedWallet2)
                
                if (conversation != null) {
                    _uiState.value = UiState.Success(validatedWallet1, validatedWallet2, conversation)
                } else {
                    _uiState.value = UiState.Error("Không thể tạo cuộc trò chuyện giữa 2 wallet này. Vui lòng kiểm tra lại.")
                }
                
            } catch (e: Exception) {
                _uiState.value = UiState.Error("Lỗi kết nối: ${e.message}")
            }
        }
    }

    private fun validateWalletAddress(address: String): String? {
        val cleanAddress = address.trim().lowercase()
        
        // Check if address starts with 0x and has 42 characters total (0x + 40 hex chars)
        return if (cleanAddress.matches(Regex("^0x[a-f0-9]{40}$"))) {
            cleanAddress
        } else if (cleanAddress.matches(Regex("^[a-f0-9]{40}$"))) {
            // Add 0x prefix if missing
            "0x$cleanAddress"
        } else {
            null
        }
    }

    private suspend fun createConversationBetweenWallets(wallet1: String, wallet2: String): Conversation? {
        return try {
            // Get the current client's address
            val currentClientAddress = ClientManager.client.publicIdentity.identifier
            
            // Determine which wallet to create conversation with (the one that's not the current client)
            val targetWallet = if (currentClientAddress.equals(wallet1, ignoreCase = true)) {
                wallet2
            } else {
                wallet1
            }
            
            // Sync conversations first to get latest data
            ClientManager.client.conversations.sync()
            
            // Use findOrCreateDm instead of newConversationWithIdentity
            // This ensures both devices will connect to the same DM conversation
            val targetInboxId = getInboxIdForAddress(targetWallet)
            if (targetInboxId != null) {
                val dm = ClientManager.client.conversations.findOrCreateDm(targetInboxId)
                Conversation.Dm(dm)
            } else {
                // If target wallet doesn't have an inbox ID, try creating with identity
                val targetIdentity = PublicIdentity(IdentityKind.ETHEREUM, targetWallet)
                ClientManager.client.conversations.newConversationWithIdentity(targetIdentity)
            }
        } catch (e: Exception) {
            null
        }
    }

    private suspend fun getInboxIdForAddress(address: String): String? {
        return try {
            val targetIdentity = PublicIdentity(IdentityKind.ETHEREUM, address)
            ClientManager.client.inboxIdFromIdentity(targetIdentity)
        } catch (e: Exception) {
            null
        }
    }

    sealed class UiState {
        object Idle : UiState()
        object Loading : UiState()
        data class Success(val wallet1: String, val wallet2: String, val conversation: Conversation) : UiState()
        data class Error(val message: String) : UiState()
    }
}