package org.xmtp.android.example.wallet

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.launch
import org.xmtp.android.example.MainActivity
import org.xmtp.android.example.R
import org.xmtp.android.example.conversation.ConversationDetailActivity
import org.xmtp.android.example.databinding.ActivityWalletInputBinding

class WalletInputActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWalletInputBinding
    private val viewModel: WalletInputViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityWalletInputBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        binding.connectButton.setOnClickListener {
            val wallet1 = binding.wallet1EditText.text.toString().trim()
            val wallet2 = binding.wallet2EditText.text.toString().trim()
            
            if (wallet1.isEmpty() || wallet2.isEmpty()) {
                Toast.makeText(this, "Vui lòng nhập đầy đủ 2 địa chỉ wallet", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            viewModel.validateAndConnect(wallet1, wallet2)
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect(::handleUiState)
            }
        }
    }

    private fun handleUiState(uiState: WalletInputViewModel.UiState) {
        when (uiState) {
            is WalletInputViewModel.UiState.Loading -> {
                binding.progressBar.visibility = View.VISIBLE
                binding.connectButton.isEnabled = false
            }
            is WalletInputViewModel.UiState.Success -> {
                binding.progressBar.visibility = View.GONE
                binding.connectButton.isEnabled = true
                Toast.makeText(this, "Kết nối thành công!", Toast.LENGTH_SHORT).show()
                navigateToConversation(uiState.conversation)
            }
            is WalletInputViewModel.UiState.Error -> {
                binding.progressBar.visibility = View.GONE
                binding.connectButton.isEnabled = true
                Toast.makeText(this, uiState.message, Toast.LENGTH_LONG).show()
            }
            is WalletInputViewModel.UiState.Idle -> {
                binding.progressBar.visibility = View.GONE
                binding.connectButton.isEnabled = true
            }
        }
    }

    private fun navigateToConversation(conversation: org.xmtp.android.library.Conversation) {
        val intent = ConversationDetailActivity.intent(
            this,
            topic = conversation.topic,
            peerAddress = conversation.id
        )
        startActivity(intent)
        finish() // Đóng WalletInputActivity
    }
}