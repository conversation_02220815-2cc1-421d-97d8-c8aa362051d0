<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <queries>
        <package android:name="io.metamask"/>
        <package android:name="com.wallet.crypto.trustapp"/>
        <package android:name="io.gnosis.safe"/>
        <package android:name="me.rainbow"/>
        <package android:name="im.token.app"/>
        <package android:name="io.zerion.android"/>
        <package android:name="com.spot.spot"/>
        <package android:name="fi.steakwallet.app"/>
        <package android:name="vip.mytokenpocket"/>
        <package android:name="com.frontierwallet"/>
        <package android:name="com.bitkeep.wallet"/>
        <package android:name="im.argent.contractwalletclient"/>
        <package android:name="com.walletconnect.sample.wallet"/>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="wc" />
        </intent>
        <provider
            android:authorities="list"
            android:exported="false" />
    </queries>

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:name=".ExampleApp"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.XMTPAndroid"
        tools:targetApi="31"
        tools:replace="dataExtractionRules">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.XMTPAndroid">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".wallet.WalletInputActivity"
            android:exported="true"
            android:theme="@style/Theme.XMTPAndroid" />
        <activity
            android:name=".connect.ConnectWalletActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:scheme="xmtp-example-wc"
                    android:host="request" />
            </intent-filter>
        </activity>
        <activity
            android:name=".conversation.ConversationDetailActivity"
            android:exported="false" />

        <service
            android:name=".account.AuthenticatorService"
            android:exported="false">
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator" />
            </intent-filter>
            <meta-data
                android:name="android.accounts.AccountAuthenticator"
                android:resource="@xml/authenticator" />
        </service>

        <service
            android:name=".pushnotifications.PushNotificationsService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>
</manifest>
