<resources>
    <string name="app_name">XMTP Example</string>
    <string name="account_type">org.xmtp.example.android</string>
    <string name="error">Something went wrong</string>

    <string name="wallet_connect">WalletConnect</string>
    <string name="generate_wallet">Generate wallet</string>
    <string name="connect_wallet">Connect wallet</string>
    <string name="no_wallet_apps">No wallet apps installed</string>
    <string name="disconnect_wallet">Disconnect wallet</string>
    <string name="copy_address">Copy address</string>
    <string name="address_copied">Address copied to clipboard</string>
    <string name="address_pasted">Address pasted</string>
    <string name="invalid_address_format">Invalid address format</string>
    <string name="my_inbox_id">My Inbox ID: %1$s</string>
    <string name="tap_to_copy_inbox_id">Tap to copy your Inbox ID</string>

    <!-- Conversations -->
    <string name="conversation_footer">Signed in as %1$s on %2$s</string>
    <string name="new_message">New message</string>
    <string name="enter_address">Enter Ethereum address</string>
    <string name="create_conversation">Create conversation</string>
    <string name="empty_message">No messages yet</string>
    <string name="your_message_body">You: %1$s</string>
    <string name="xmtp_direct_message">XMTP Direct Message</string>

    <!-- Messages -->
    <string name="message_composer_hint">Type a message…</string>

    <!-- Logs -->
    <string name="view_logs">View Logs</string>
    <string name="share_log">Share Log</string>
    <string name="no_logs_found">No log files found</string>
    <string name="activate_logs">Activate Persistent Logs</string>
    <string name="deactivate_logs">Deactivate Persistent Logs</string>
    
</resources>
