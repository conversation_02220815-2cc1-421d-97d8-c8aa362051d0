<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/messageRow"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- Margin and constraint added programatically based on who sent the message -->
    <androidx.cardview.widget.CardView
        android:id="@+id/messageContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding"
        app:cardCornerRadius="8dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/messageBody"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:layout_margin="8dp" />
            <TextView
                android:id="@+id/messageDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:layout_margin="8dp"
                />
        </LinearLayout>



    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
