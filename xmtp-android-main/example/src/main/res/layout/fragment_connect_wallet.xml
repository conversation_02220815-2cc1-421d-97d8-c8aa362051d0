<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".connect.ConnectWalletFragment">

    <ProgressBar
        android:id="@+id/progress"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/generateButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding"
        android:text="@string/generate_wallet"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/connectButton"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        style="?attr/materialButtonOutlinedStyle"
        android:text="@string/wallet_connect"
        android:textColor="#FF006FFF"
        app:icon="@drawable/ic_wallet_connect_logo"
        app:iconTint="#FF006FFF"
        app:iconGravity="textStart"
        app:strokeColor="#FF006FFF"
        android:layout_marginStart="@dimen/padding"
        android:layout_marginEnd="@dimen/padding"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/generateButton" />

    <TextView
        android:id="@+id/connectError"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding"
        android:layout_marginEnd="@dimen/padding"
        android:gravity="center"
        android:text="@string/no_wallet_apps"
        android:textColor="@android:color/holo_red_dark"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/connectButton" />

</androidx.constraintlayout.widget.ConstraintLayout>
