<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground">

    <TextView
        android:id="@+id/peerAddress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/padding"
        android:paddingStart="@dimen/padding"
        android:paddingEnd="@dimen/padding"
        android:textColor="@android:color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/messageBody"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/padding"
        android:paddingEnd="@dimen/padding"
        android:paddingBottom="@dimen/padding"
        android:textSize="12sp"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/peerAddress" />

</androidx.constraintlayout.widget.ConstraintLayout>
