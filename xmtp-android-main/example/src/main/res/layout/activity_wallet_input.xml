<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    tools:context=".wallet.WalletInputActivity">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Nhập Địa Chỉ Wallet"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="32dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/wallet1Label" />

    <TextView
        android:id="@+id/wallet1Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Địa chỉ Wallet 1:"
        android:textSize="16sp"
        android:layout_marginBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/titleTextView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/wallet1InputLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="0x..."
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_marginBottom="24dp"
        app:layout_constraintTop_toBottomOf="@id/wallet1Label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/wallet1EditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:fontFamily="monospace" />

    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/wallet2Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Địa chỉ Wallet 2:"
        android:textSize="16sp"
        android:layout_marginBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/wallet1InputLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/wallet2InputLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="0x..."
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_marginBottom="32dp"
        app:layout_constraintTop_toBottomOf="@id/wallet2Label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/wallet2EditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:fontFamily="monospace" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/connectButton"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:text="Kết Nối"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@id/wallet2InputLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/connectButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="24dp" />

    <TextView
        android:id="@+id/instructionTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Nhập 2 địa chỉ Ethereum wallet để kiểm tra khả năng gửi tin nhắn"
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray"
        android:gravity="center"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>