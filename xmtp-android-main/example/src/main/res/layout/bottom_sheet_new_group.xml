<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/padding"
        android:paddingBottom="48dp">

        <ProgressBar
            android:id="@+id/progress"
            style="@style/Widget.AppCompat.ProgressBar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/addressInput1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding"
            android:autofillHints="no"
            android:hint="@string/enter_address"
            android:inputType="text"
            android:maxLines="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@id/addressInput2"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/addressInput2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding"
            android:autofillHints="no"
            android:hint="@string/enter_address"
            android:inputType="text"
            android:maxLines="1"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/addressInput1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>

