// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: notifications/v1/service.proto

package org.xmtp.android.library.push;

public final class Service {
    private Service() {}
    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistryLite registry) {
    }
    public interface DeliveryMechanismOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.DeliveryMechanism)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string apns_device_token = 1;</code>
         * @return Whether the apnsDeviceToken field is set.
         */
        boolean hasApnsDeviceToken();
        /**
         * <code>string apns_device_token = 1;</code>
         * @return The apnsDeviceToken.
         */
        java.lang.String getApnsDeviceToken();
        /**
         * <code>string apns_device_token = 1;</code>
         * @return The bytes for apnsDeviceToken.
         */
        com.google.protobuf.ByteString
        getApnsDeviceTokenBytes();

        /**
         * <code>string firebase_device_token = 2;</code>
         * @return Whether the firebaseDeviceToken field is set.
         */
        boolean hasFirebaseDeviceToken();
        /**
         * <code>string firebase_device_token = 2;</code>
         * @return The firebaseDeviceToken.
         */
        java.lang.String getFirebaseDeviceToken();
        /**
         * <code>string firebase_device_token = 2;</code>
         * @return The bytes for firebaseDeviceToken.
         */
        com.google.protobuf.ByteString
        getFirebaseDeviceTokenBytes();

        /**
         * <code>string custom_token = 3;</code>
         * @return Whether the customToken field is set.
         */
        boolean hasCustomToken();
        /**
         * <code>string custom_token = 3;</code>
         * @return The customToken.
         */
        java.lang.String getCustomToken();
        /**
         * <code>string custom_token = 3;</code>
         * @return The bytes for customToken.
         */
        com.google.protobuf.ByteString
        getCustomTokenBytes();

        public org.xmtp.android.library.push.Service.DeliveryMechanism.DeliveryMechanismTypeCase getDeliveryMechanismTypeCase();
    }
    /**
     * <pre>
     * An union of possible delibery mechanisms
     * </pre>
     *
     * Protobuf type {@code notifications.v1.DeliveryMechanism}
     */
    public  static final class DeliveryMechanism extends
            com.google.protobuf.GeneratedMessageLite<
                    DeliveryMechanism, DeliveryMechanism.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.DeliveryMechanism)
            DeliveryMechanismOrBuilder {
        private DeliveryMechanism() {
        }
        private int deliveryMechanismTypeCase_ = 0;
        private java.lang.Object deliveryMechanismType_;
        public enum DeliveryMechanismTypeCase {
            APNS_DEVICE_TOKEN(1),
            FIREBASE_DEVICE_TOKEN(2),
            CUSTOM_TOKEN(3),
            DELIVERYMECHANISMTYPE_NOT_SET(0);
            private final int value;
            private DeliveryMechanismTypeCase(int value) {
                this.value = value;
            }
            /**
             * @deprecated Use {@link #forNumber(int)} instead.
             */
            @java.lang.Deprecated
            public static DeliveryMechanismTypeCase valueOf(int value) {
                return forNumber(value);
            }

            public static DeliveryMechanismTypeCase forNumber(int value) {
                switch (value) {
                    case 1: return APNS_DEVICE_TOKEN;
                    case 2: return FIREBASE_DEVICE_TOKEN;
                    case 3: return CUSTOM_TOKEN;
                    case 0: return DELIVERYMECHANISMTYPE_NOT_SET;
                    default: return null;
                }
            }
            public int getNumber() {
                return this.value;
            }
        };

        @java.lang.Override
        public DeliveryMechanismTypeCase
        getDeliveryMechanismTypeCase() {
            return DeliveryMechanismTypeCase.forNumber(
                    deliveryMechanismTypeCase_);
        }

        private void clearDeliveryMechanismType() {
            deliveryMechanismTypeCase_ = 0;
            deliveryMechanismType_ = null;
        }

        public static final int APNS_DEVICE_TOKEN_FIELD_NUMBER = 1;
        /**
         * <code>string apns_device_token = 1;</code>
         * @return Whether the apnsDeviceToken field is set.
         */
        @java.lang.Override
        public boolean hasApnsDeviceToken() {
            return deliveryMechanismTypeCase_ == 1;
        }
        /**
         * <code>string apns_device_token = 1;</code>
         * @return The apnsDeviceToken.
         */
        @java.lang.Override
        public java.lang.String getApnsDeviceToken() {
            java.lang.String ref = "";
            if (deliveryMechanismTypeCase_ == 1) {
                ref = (java.lang.String) deliveryMechanismType_;
            }
            return ref;
        }
        /**
         * <code>string apns_device_token = 1;</code>
         * @return The bytes for apnsDeviceToken.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getApnsDeviceTokenBytes() {
            java.lang.String ref = "";
            if (deliveryMechanismTypeCase_ == 1) {
                ref = (java.lang.String) deliveryMechanismType_;
            }
            return com.google.protobuf.ByteString.copyFromUtf8(ref);
        }
        /**
         * <code>string apns_device_token = 1;</code>
         * @param value The apnsDeviceToken to set.
         */
        private void setApnsDeviceToken(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();
            deliveryMechanismTypeCase_ = 1;
            deliveryMechanismType_ = value;
        }
        /**
         * <code>string apns_device_token = 1;</code>
         */
        private void clearApnsDeviceToken() {
            if (deliveryMechanismTypeCase_ == 1) {
                deliveryMechanismTypeCase_ = 0;
                deliveryMechanismType_ = null;
            }
        }
        /**
         * <code>string apns_device_token = 1;</code>
         * @param value The bytes for apnsDeviceToken to set.
         */
        private void setApnsDeviceTokenBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            deliveryMechanismType_ = value.toStringUtf8();
            deliveryMechanismTypeCase_ = 1;
        }

        public static final int FIREBASE_DEVICE_TOKEN_FIELD_NUMBER = 2;
        /**
         * <code>string firebase_device_token = 2;</code>
         * @return Whether the firebaseDeviceToken field is set.
         */
        @java.lang.Override
        public boolean hasFirebaseDeviceToken() {
            return deliveryMechanismTypeCase_ == 2;
        }
        /**
         * <code>string firebase_device_token = 2;</code>
         * @return The firebaseDeviceToken.
         */
        @java.lang.Override
        public java.lang.String getFirebaseDeviceToken() {
            java.lang.String ref = "";
            if (deliveryMechanismTypeCase_ == 2) {
                ref = (java.lang.String) deliveryMechanismType_;
            }
            return ref;
        }
        /**
         * <code>string firebase_device_token = 2;</code>
         * @return The bytes for firebaseDeviceToken.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getFirebaseDeviceTokenBytes() {
            java.lang.String ref = "";
            if (deliveryMechanismTypeCase_ == 2) {
                ref = (java.lang.String) deliveryMechanismType_;
            }
            return com.google.protobuf.ByteString.copyFromUtf8(ref);
        }
        /**
         * <code>string firebase_device_token = 2;</code>
         * @param value The firebaseDeviceToken to set.
         */
        private void setFirebaseDeviceToken(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();
            deliveryMechanismTypeCase_ = 2;
            deliveryMechanismType_ = value;
        }
        /**
         * <code>string firebase_device_token = 2;</code>
         */
        private void clearFirebaseDeviceToken() {
            if (deliveryMechanismTypeCase_ == 2) {
                deliveryMechanismTypeCase_ = 0;
                deliveryMechanismType_ = null;
            }
        }
        /**
         * <code>string firebase_device_token = 2;</code>
         * @param value The bytes for firebaseDeviceToken to set.
         */
        private void setFirebaseDeviceTokenBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            deliveryMechanismType_ = value.toStringUtf8();
            deliveryMechanismTypeCase_ = 2;
        }

        public static final int CUSTOM_TOKEN_FIELD_NUMBER = 3;
        /**
         * <code>string custom_token = 3;</code>
         * @return Whether the customToken field is set.
         */
        @java.lang.Override
        public boolean hasCustomToken() {
            return deliveryMechanismTypeCase_ == 3;
        }
        /**
         * <code>string custom_token = 3;</code>
         * @return The customToken.
         */
        @java.lang.Override
        public java.lang.String getCustomToken() {
            java.lang.String ref = "";
            if (deliveryMechanismTypeCase_ == 3) {
                ref = (java.lang.String) deliveryMechanismType_;
            }
            return ref;
        }
        /**
         * <code>string custom_token = 3;</code>
         * @return The bytes for customToken.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getCustomTokenBytes() {
            java.lang.String ref = "";
            if (deliveryMechanismTypeCase_ == 3) {
                ref = (java.lang.String) deliveryMechanismType_;
            }
            return com.google.protobuf.ByteString.copyFromUtf8(ref);
        }
        /**
         * <code>string custom_token = 3;</code>
         * @param value The customToken to set.
         */
        private void setCustomToken(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();
            deliveryMechanismTypeCase_ = 3;
            deliveryMechanismType_ = value;
        }
        /**
         * <code>string custom_token = 3;</code>
         */
        private void clearCustomToken() {
            if (deliveryMechanismTypeCase_ == 3) {
                deliveryMechanismTypeCase_ = 0;
                deliveryMechanismType_ = null;
            }
        }
        /**
         * <code>string custom_token = 3;</code>
         * @param value The bytes for customToken to set.
         */
        private void setCustomTokenBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            deliveryMechanismType_ = value.toStringUtf8();
            deliveryMechanismTypeCase_ = 3;
        }

        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.DeliveryMechanism parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.DeliveryMechanism prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * An union of possible delibery mechanisms
         * </pre>
         *
         * Protobuf type {@code notifications.v1.DeliveryMechanism}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.DeliveryMechanism, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.DeliveryMechanism)
                org.xmtp.android.library.push.Service.DeliveryMechanismOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.DeliveryMechanism.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }

            @java.lang.Override
            public DeliveryMechanismTypeCase
            getDeliveryMechanismTypeCase() {
                return instance.getDeliveryMechanismTypeCase();
            }

            public Builder clearDeliveryMechanismType() {
                copyOnWrite();
                instance.clearDeliveryMechanismType();
                return this;
            }


            /**
             * <code>string apns_device_token = 1;</code>
             * @return Whether the apnsDeviceToken field is set.
             */
            @java.lang.Override
            public boolean hasApnsDeviceToken() {
                return instance.hasApnsDeviceToken();
            }
            /**
             * <code>string apns_device_token = 1;</code>
             * @return The apnsDeviceToken.
             */
            @java.lang.Override
            public java.lang.String getApnsDeviceToken() {
                return instance.getApnsDeviceToken();
            }
            /**
             * <code>string apns_device_token = 1;</code>
             * @return The bytes for apnsDeviceToken.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getApnsDeviceTokenBytes() {
                return instance.getApnsDeviceTokenBytes();
            }
            /**
             * <code>string apns_device_token = 1;</code>
             * @param value The apnsDeviceToken to set.
             * @return This builder for chaining.
             */
            public Builder setApnsDeviceToken(
                    java.lang.String value) {
                copyOnWrite();
                instance.setApnsDeviceToken(value);
                return this;
            }
            /**
             * <code>string apns_device_token = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearApnsDeviceToken() {
                copyOnWrite();
                instance.clearApnsDeviceToken();
                return this;
            }
            /**
             * <code>string apns_device_token = 1;</code>
             * @param value The bytes for apnsDeviceToken to set.
             * @return This builder for chaining.
             */
            public Builder setApnsDeviceTokenBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setApnsDeviceTokenBytes(value);
                return this;
            }

            /**
             * <code>string firebase_device_token = 2;</code>
             * @return Whether the firebaseDeviceToken field is set.
             */
            @java.lang.Override
            public boolean hasFirebaseDeviceToken() {
                return instance.hasFirebaseDeviceToken();
            }
            /**
             * <code>string firebase_device_token = 2;</code>
             * @return The firebaseDeviceToken.
             */
            @java.lang.Override
            public java.lang.String getFirebaseDeviceToken() {
                return instance.getFirebaseDeviceToken();
            }
            /**
             * <code>string firebase_device_token = 2;</code>
             * @return The bytes for firebaseDeviceToken.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getFirebaseDeviceTokenBytes() {
                return instance.getFirebaseDeviceTokenBytes();
            }
            /**
             * <code>string firebase_device_token = 2;</code>
             * @param value The firebaseDeviceToken to set.
             * @return This builder for chaining.
             */
            public Builder setFirebaseDeviceToken(
                    java.lang.String value) {
                copyOnWrite();
                instance.setFirebaseDeviceToken(value);
                return this;
            }
            /**
             * <code>string firebase_device_token = 2;</code>
             * @return This builder for chaining.
             */
            public Builder clearFirebaseDeviceToken() {
                copyOnWrite();
                instance.clearFirebaseDeviceToken();
                return this;
            }
            /**
             * <code>string firebase_device_token = 2;</code>
             * @param value The bytes for firebaseDeviceToken to set.
             * @return This builder for chaining.
             */
            public Builder setFirebaseDeviceTokenBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setFirebaseDeviceTokenBytes(value);
                return this;
            }

            /**
             * <code>string custom_token = 3;</code>
             * @return Whether the customToken field is set.
             */
            @java.lang.Override
            public boolean hasCustomToken() {
                return instance.hasCustomToken();
            }
            /**
             * <code>string custom_token = 3;</code>
             * @return The customToken.
             */
            @java.lang.Override
            public java.lang.String getCustomToken() {
                return instance.getCustomToken();
            }
            /**
             * <code>string custom_token = 3;</code>
             * @return The bytes for customToken.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getCustomTokenBytes() {
                return instance.getCustomTokenBytes();
            }
            /**
             * <code>string custom_token = 3;</code>
             * @param value The customToken to set.
             * @return This builder for chaining.
             */
            public Builder setCustomToken(
                    java.lang.String value) {
                copyOnWrite();
                instance.setCustomToken(value);
                return this;
            }
            /**
             * <code>string custom_token = 3;</code>
             * @return This builder for chaining.
             */
            public Builder clearCustomToken() {
                copyOnWrite();
                instance.clearCustomToken();
                return this;
            }
            /**
             * <code>string custom_token = 3;</code>
             * @param value The bytes for customToken to set.
             * @return This builder for chaining.
             */
            public Builder setCustomTokenBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setCustomTokenBytes(value);
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.DeliveryMechanism)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.DeliveryMechanism();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "deliveryMechanismType_",
                            "deliveryMechanismTypeCase_",
                    };
                    java.lang.String info =
                            "\u0000\u0003\u0001\u0000\u0001\u0003\u0003\u0000\u0000\u0000\u0001\u023b\u0000\u0002" +
                                    "\u023b\u0000\u0003\u023b\u0000";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.DeliveryMechanism> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.DeliveryMechanism.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.DeliveryMechanism>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.DeliveryMechanism)
        private static final org.xmtp.android.library.push.Service.DeliveryMechanism DEFAULT_INSTANCE;
        static {
            DeliveryMechanism defaultInstance = new DeliveryMechanism();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    DeliveryMechanism.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.DeliveryMechanism getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<DeliveryMechanism> PARSER;

        public static com.google.protobuf.Parser<DeliveryMechanism> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }

    public interface RegisterInstallationRequestOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.RegisterInstallationRequest)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        java.lang.String getInstallationId();
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        com.google.protobuf.ByteString
        getInstallationIdBytes();

        /**
         * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
         * @return Whether the deliveryMechanism field is set.
         */
        boolean hasDeliveryMechanism();
        /**
         * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
         * @return The deliveryMechanism.
         */
        org.xmtp.android.library.push.Service.DeliveryMechanism getDeliveryMechanism();
    }
    /**
     * <pre>
     * A request to register an installation with the service
     * </pre>
     *
     * Protobuf type {@code notifications.v1.RegisterInstallationRequest}
     */
    public  static final class RegisterInstallationRequest extends
            com.google.protobuf.GeneratedMessageLite<
                    RegisterInstallationRequest, RegisterInstallationRequest.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.RegisterInstallationRequest)
            RegisterInstallationRequestOrBuilder {
        private RegisterInstallationRequest() {
            installationId_ = "";
        }
        public static final int INSTALLATION_ID_FIELD_NUMBER = 1;
        private java.lang.String installationId_;
        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        @java.lang.Override
        public java.lang.String getInstallationId() {
            return installationId_;
        }
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getInstallationIdBytes() {
            return com.google.protobuf.ByteString.copyFromUtf8(installationId_);
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The installationId to set.
         */
        private void setInstallationId(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();

            installationId_ = value;
        }
        /**
         * <code>string installation_id = 1;</code>
         */
        private void clearInstallationId() {

            installationId_ = getDefaultInstance().getInstallationId();
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The bytes for installationId to set.
         */
        private void setInstallationIdBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            installationId_ = value.toStringUtf8();

        }

        public static final int DELIVERY_MECHANISM_FIELD_NUMBER = 2;
        private org.xmtp.android.library.push.Service.DeliveryMechanism deliveryMechanism_;
        /**
         * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
         */
        @java.lang.Override
        public boolean hasDeliveryMechanism() {
            return deliveryMechanism_ != null;
        }
        /**
         * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
         */
        @java.lang.Override
        public org.xmtp.android.library.push.Service.DeliveryMechanism getDeliveryMechanism() {
            return deliveryMechanism_ == null ? org.xmtp.android.library.push.Service.DeliveryMechanism.getDefaultInstance() : deliveryMechanism_;
        }
        /**
         * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
         */
        private void setDeliveryMechanism(org.xmtp.android.library.push.Service.DeliveryMechanism value) {
            value.getClass();
            deliveryMechanism_ = value;

        }
        /**
         * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
         */
        @java.lang.SuppressWarnings({"ReferenceEquality"})
        private void mergeDeliveryMechanism(org.xmtp.android.library.push.Service.DeliveryMechanism value) {
            value.getClass();
            if (deliveryMechanism_ != null &&
                    deliveryMechanism_ != org.xmtp.android.library.push.Service.DeliveryMechanism.getDefaultInstance()) {
                deliveryMechanism_ =
                        org.xmtp.android.library.push.Service.DeliveryMechanism.newBuilder(deliveryMechanism_).mergeFrom(value).buildPartial();
            } else {
                deliveryMechanism_ = value;
            }

        }
        /**
         * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
         */
        private void clearDeliveryMechanism() {  deliveryMechanism_ = null;

        }

        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.RegisterInstallationRequest prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * A request to register an installation with the service
         * </pre>
         *
         * Protobuf type {@code notifications.v1.RegisterInstallationRequest}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.RegisterInstallationRequest, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.RegisterInstallationRequest)
                org.xmtp.android.library.push.Service.RegisterInstallationRequestOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.RegisterInstallationRequest.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }


            /**
             * <code>string installation_id = 1;</code>
             * @return The installationId.
             */
            @java.lang.Override
            public java.lang.String getInstallationId() {
                return instance.getInstallationId();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return The bytes for installationId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getInstallationIdBytes() {
                return instance.getInstallationIdBytes();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationId(
                    java.lang.String value) {
                copyOnWrite();
                instance.setInstallationId(value);
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearInstallationId() {
                copyOnWrite();
                instance.clearInstallationId();
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The bytes for installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationIdBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setInstallationIdBytes(value);
                return this;
            }

            /**
             * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
             */
            @java.lang.Override
            public boolean hasDeliveryMechanism() {
                return instance.hasDeliveryMechanism();
            }
            /**
             * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
             */
            @java.lang.Override
            public org.xmtp.android.library.push.Service.DeliveryMechanism getDeliveryMechanism() {
                return instance.getDeliveryMechanism();
            }
            /**
             * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
             */
            public Builder setDeliveryMechanism(org.xmtp.android.library.push.Service.DeliveryMechanism value) {
                copyOnWrite();
                instance.setDeliveryMechanism(value);
                return this;
            }
            /**
             * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
             */
            public Builder setDeliveryMechanism(
                    org.xmtp.android.library.push.Service.DeliveryMechanism.Builder builderForValue) {
                copyOnWrite();
                instance.setDeliveryMechanism(builderForValue.build());
                return this;
            }
            /**
             * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
             */
            public Builder mergeDeliveryMechanism(org.xmtp.android.library.push.Service.DeliveryMechanism value) {
                copyOnWrite();
                instance.mergeDeliveryMechanism(value);
                return this;
            }
            /**
             * <code>.notifications.v1.DeliveryMechanism delivery_mechanism = 2;</code>
             */
            public Builder clearDeliveryMechanism() {  copyOnWrite();
                instance.clearDeliveryMechanism();
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.RegisterInstallationRequest)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.RegisterInstallationRequest();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "installationId_",
                            "deliveryMechanism_",
                    };
                    java.lang.String info =
                            "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u0208\u0002\t" +
                                    "";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.RegisterInstallationRequest> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.RegisterInstallationRequest.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.RegisterInstallationRequest>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.RegisterInstallationRequest)
        private static final org.xmtp.android.library.push.Service.RegisterInstallationRequest DEFAULT_INSTANCE;
        static {
            RegisterInstallationRequest defaultInstance = new RegisterInstallationRequest();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    RegisterInstallationRequest.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.RegisterInstallationRequest getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<RegisterInstallationRequest> PARSER;

        public static com.google.protobuf.Parser<RegisterInstallationRequest> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }

    public interface RegisterInstallationResponseOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.RegisterInstallationResponse)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        java.lang.String getInstallationId();
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        com.google.protobuf.ByteString
        getInstallationIdBytes();

        /**
         * <code>uint64 valid_until = 2;</code>
         * @return The validUntil.
         */
        long getValidUntil();
    }
    /**
     * <pre>
     * Response to RegisterInstallationRequest
     * </pre>
     *
     * Protobuf type {@code notifications.v1.RegisterInstallationResponse}
     */
    public  static final class RegisterInstallationResponse extends
            com.google.protobuf.GeneratedMessageLite<
                    RegisterInstallationResponse, RegisterInstallationResponse.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.RegisterInstallationResponse)
            RegisterInstallationResponseOrBuilder {
        private RegisterInstallationResponse() {
            installationId_ = "";
        }
        public static final int INSTALLATION_ID_FIELD_NUMBER = 1;
        private java.lang.String installationId_;
        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        @java.lang.Override
        public java.lang.String getInstallationId() {
            return installationId_;
        }
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getInstallationIdBytes() {
            return com.google.protobuf.ByteString.copyFromUtf8(installationId_);
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The installationId to set.
         */
        private void setInstallationId(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();

            installationId_ = value;
        }
        /**
         * <code>string installation_id = 1;</code>
         */
        private void clearInstallationId() {

            installationId_ = getDefaultInstance().getInstallationId();
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The bytes for installationId to set.
         */
        private void setInstallationIdBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            installationId_ = value.toStringUtf8();

        }

        public static final int VALID_UNTIL_FIELD_NUMBER = 2;
        private long validUntil_;
        /**
         * <code>uint64 valid_until = 2;</code>
         * @return The validUntil.
         */
        @java.lang.Override
        public long getValidUntil() {
            return validUntil_;
        }
        /**
         * <code>uint64 valid_until = 2;</code>
         * @param value The validUntil to set.
         */
        private void setValidUntil(long value) {

            validUntil_ = value;
        }
        /**
         * <code>uint64 valid_until = 2;</code>
         */
        private void clearValidUntil() {

            validUntil_ = 0L;
        }

        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.RegisterInstallationResponse prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * Response to RegisterInstallationRequest
         * </pre>
         *
         * Protobuf type {@code notifications.v1.RegisterInstallationResponse}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.RegisterInstallationResponse, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.RegisterInstallationResponse)
                org.xmtp.android.library.push.Service.RegisterInstallationResponseOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.RegisterInstallationResponse.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }


            /**
             * <code>string installation_id = 1;</code>
             * @return The installationId.
             */
            @java.lang.Override
            public java.lang.String getInstallationId() {
                return instance.getInstallationId();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return The bytes for installationId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getInstallationIdBytes() {
                return instance.getInstallationIdBytes();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationId(
                    java.lang.String value) {
                copyOnWrite();
                instance.setInstallationId(value);
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearInstallationId() {
                copyOnWrite();
                instance.clearInstallationId();
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The bytes for installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationIdBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setInstallationIdBytes(value);
                return this;
            }

            /**
             * <code>uint64 valid_until = 2;</code>
             * @return The validUntil.
             */
            @java.lang.Override
            public long getValidUntil() {
                return instance.getValidUntil();
            }
            /**
             * <code>uint64 valid_until = 2;</code>
             * @param value The validUntil to set.
             * @return This builder for chaining.
             */
            public Builder setValidUntil(long value) {
                copyOnWrite();
                instance.setValidUntil(value);
                return this;
            }
            /**
             * <code>uint64 valid_until = 2;</code>
             * @return This builder for chaining.
             */
            public Builder clearValidUntil() {
                copyOnWrite();
                instance.clearValidUntil();
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.RegisterInstallationResponse)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.RegisterInstallationResponse();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "installationId_",
                            "validUntil_",
                    };
                    java.lang.String info =
                            "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u0208\u0002\u0003" +
                                    "";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.RegisterInstallationResponse> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.RegisterInstallationResponse.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.RegisterInstallationResponse>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.RegisterInstallationResponse)
        private static final org.xmtp.android.library.push.Service.RegisterInstallationResponse DEFAULT_INSTANCE;
        static {
            RegisterInstallationResponse defaultInstance = new RegisterInstallationResponse();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    RegisterInstallationResponse.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.RegisterInstallationResponse getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<RegisterInstallationResponse> PARSER;

        public static com.google.protobuf.Parser<RegisterInstallationResponse> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }

    public interface DeleteInstallationRequestOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.DeleteInstallationRequest)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        java.lang.String getInstallationId();
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        com.google.protobuf.ByteString
        getInstallationIdBytes();
    }
    /**
     * <pre>
     * Delete an installation from the service
     * </pre>
     *
     * Protobuf type {@code notifications.v1.DeleteInstallationRequest}
     */
    public  static final class DeleteInstallationRequest extends
            com.google.protobuf.GeneratedMessageLite<
                    DeleteInstallationRequest, DeleteInstallationRequest.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.DeleteInstallationRequest)
            DeleteInstallationRequestOrBuilder {
        private DeleteInstallationRequest() {
            installationId_ = "";
        }
        public static final int INSTALLATION_ID_FIELD_NUMBER = 1;
        private java.lang.String installationId_;
        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        @java.lang.Override
        public java.lang.String getInstallationId() {
            return installationId_;
        }
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getInstallationIdBytes() {
            return com.google.protobuf.ByteString.copyFromUtf8(installationId_);
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The installationId to set.
         */
        private void setInstallationId(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();

            installationId_ = value;
        }
        /**
         * <code>string installation_id = 1;</code>
         */
        private void clearInstallationId() {

            installationId_ = getDefaultInstance().getInstallationId();
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The bytes for installationId to set.
         */
        private void setInstallationIdBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            installationId_ = value.toStringUtf8();

        }

        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.DeleteInstallationRequest prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * Delete an installation from the service
         * </pre>
         *
         * Protobuf type {@code notifications.v1.DeleteInstallationRequest}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.DeleteInstallationRequest, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.DeleteInstallationRequest)
                org.xmtp.android.library.push.Service.DeleteInstallationRequestOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.DeleteInstallationRequest.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }


            /**
             * <code>string installation_id = 1;</code>
             * @return The installationId.
             */
            @java.lang.Override
            public java.lang.String getInstallationId() {
                return instance.getInstallationId();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return The bytes for installationId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getInstallationIdBytes() {
                return instance.getInstallationIdBytes();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationId(
                    java.lang.String value) {
                copyOnWrite();
                instance.setInstallationId(value);
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearInstallationId() {
                copyOnWrite();
                instance.clearInstallationId();
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The bytes for installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationIdBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setInstallationIdBytes(value);
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.DeleteInstallationRequest)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.DeleteInstallationRequest();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "installationId_",
                    };
                    java.lang.String info =
                            "\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0208";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.DeleteInstallationRequest> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.DeleteInstallationRequest.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.DeleteInstallationRequest>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.DeleteInstallationRequest)
        private static final org.xmtp.android.library.push.Service.DeleteInstallationRequest DEFAULT_INSTANCE;
        static {
            DeleteInstallationRequest defaultInstance = new DeleteInstallationRequest();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    DeleteInstallationRequest.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.DeleteInstallationRequest getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<DeleteInstallationRequest> PARSER;

        public static com.google.protobuf.Parser<DeleteInstallationRequest> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }

    public interface SubscriptionOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.Subscription)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string topic = 1;</code>
         * @return The topic.
         */
        java.lang.String getTopic();
        /**
         * <code>string topic = 1;</code>
         * @return The bytes for topic.
         */
        com.google.protobuf.ByteString
        getTopicBytes();

        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        java.util.List<org.xmtp.android.library.push.Service.Subscription.HmacKey>
        getHmacKeysList();
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        org.xmtp.android.library.push.Service.Subscription.HmacKey getHmacKeys(int index);
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        int getHmacKeysCount();

        /**
         * <code>bool is_silent = 3;</code>
         * @return The isSilent.
         */
        boolean getIsSilent();
    }
    /**
     * <pre>
     * A subscription with associated metadata
     * </pre>
     *
     * Protobuf type {@code notifications.v1.Subscription}
     */
    public  static final class Subscription extends
            com.google.protobuf.GeneratedMessageLite<
                    Subscription, Subscription.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.Subscription)
            SubscriptionOrBuilder {
        private Subscription() {
            topic_ = "";
            hmacKeys_ = emptyProtobufList();
        }
        public interface HmacKeyOrBuilder extends
                // @@protoc_insertion_point(interface_extends:notifications.v1.Subscription.HmacKey)
                com.google.protobuf.MessageLiteOrBuilder {

            /**
             * <code>uint32 thirty_day_periods_since_epoch = 1;</code>
             * @return The thirtyDayPeriodsSinceEpoch.
             */
            int getThirtyDayPeriodsSinceEpoch();

            /**
             * <code>bytes key = 2;</code>
             * @return The key.
             */
            com.google.protobuf.ByteString getKey();
        }
        /**
         * Protobuf type {@code notifications.v1.Subscription.HmacKey}
         */
        public  static final class HmacKey extends
                com.google.protobuf.GeneratedMessageLite<
                        HmacKey, HmacKey.Builder> implements
                // @@protoc_insertion_point(message_implements:notifications.v1.Subscription.HmacKey)
                HmacKeyOrBuilder {
            private HmacKey() {
                key_ = com.google.protobuf.ByteString.EMPTY;
            }
            public static final int THIRTY_DAY_PERIODS_SINCE_EPOCH_FIELD_NUMBER = 1;
            private int thirtyDayPeriodsSinceEpoch_;
            /**
             * <code>uint32 thirty_day_periods_since_epoch = 1;</code>
             * @return The thirtyDayPeriodsSinceEpoch.
             */
            @java.lang.Override
            public int getThirtyDayPeriodsSinceEpoch() {
                return thirtyDayPeriodsSinceEpoch_;
            }
            /**
             * <code>uint32 thirty_day_periods_since_epoch = 1;</code>
             * @param value The thirtyDayPeriodsSinceEpoch to set.
             */
            private void setThirtyDayPeriodsSinceEpoch(int value) {

                thirtyDayPeriodsSinceEpoch_ = value;
            }
            /**
             * <code>uint32 thirty_day_periods_since_epoch = 1;</code>
             */
            private void clearThirtyDayPeriodsSinceEpoch() {

                thirtyDayPeriodsSinceEpoch_ = 0;
            }

            public static final int KEY_FIELD_NUMBER = 2;
            private com.google.protobuf.ByteString key_;
            /**
             * <code>bytes key = 2;</code>
             * @return The key.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString getKey() {
                return key_;
            }
            /**
             * <code>bytes key = 2;</code>
             * @param value The key to set.
             */
            private void setKey(com.google.protobuf.ByteString value) {
                java.lang.Class<?> valueClass = value.getClass();

                key_ = value;
            }
            /**
             * <code>bytes key = 2;</code>
             */
            private void clearKey() {

                key_ = getDefaultInstance().getKey();
            }

            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    java.nio.ByteBuffer data)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, data);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    java.nio.ByteBuffer data,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, data, extensionRegistry);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    com.google.protobuf.ByteString data)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, data);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    com.google.protobuf.ByteString data,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, data, extensionRegistry);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(byte[] data)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, data);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    byte[] data,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, data, extensionRegistry);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(java.io.InputStream input)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, input);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    java.io.InputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, input, extensionRegistry);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseDelimitedFrom(java.io.InputStream input)
                    throws java.io.IOException {
                return parseDelimitedFrom(DEFAULT_INSTANCE, input);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseDelimitedFrom(
                    java.io.InputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    com.google.protobuf.CodedInputStream input)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, input);
            }
            public static org.xmtp.android.library.push.Service.Subscription.HmacKey parseFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageLite.parseFrom(
                        DEFAULT_INSTANCE, input, extensionRegistry);
            }

            public static Builder newBuilder() {
                return (Builder) DEFAULT_INSTANCE.createBuilder();
            }
            public static Builder newBuilder(org.xmtp.android.library.push.Service.Subscription.HmacKey prototype) {
                return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
            }

            /**
             * Protobuf type {@code notifications.v1.Subscription.HmacKey}
             */
            public static final class Builder extends
                    com.google.protobuf.GeneratedMessageLite.Builder<
                            org.xmtp.android.library.push.Service.Subscription.HmacKey, Builder> implements
                    // @@protoc_insertion_point(builder_implements:notifications.v1.Subscription.HmacKey)
                    org.xmtp.android.library.push.Service.Subscription.HmacKeyOrBuilder {
                // Construct using org.xmtp.android.library.push.Service.Subscription.HmacKey.newBuilder()
                private Builder() {
                    super(DEFAULT_INSTANCE);
                }


                /**
                 * <code>uint32 thirty_day_periods_since_epoch = 1;</code>
                 * @return The thirtyDayPeriodsSinceEpoch.
                 */
                @java.lang.Override
                public int getThirtyDayPeriodsSinceEpoch() {
                    return instance.getThirtyDayPeriodsSinceEpoch();
                }
                /**
                 * <code>uint32 thirty_day_periods_since_epoch = 1;</code>
                 * @param value The thirtyDayPeriodsSinceEpoch to set.
                 * @return This builder for chaining.
                 */
                public Builder setThirtyDayPeriodsSinceEpoch(int value) {
                    copyOnWrite();
                    instance.setThirtyDayPeriodsSinceEpoch(value);
                    return this;
                }
                /**
                 * <code>uint32 thirty_day_periods_since_epoch = 1;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearThirtyDayPeriodsSinceEpoch() {
                    copyOnWrite();
                    instance.clearThirtyDayPeriodsSinceEpoch();
                    return this;
                }

                /**
                 * <code>bytes key = 2;</code>
                 * @return The key.
                 */
                @java.lang.Override
                public com.google.protobuf.ByteString getKey() {
                    return instance.getKey();
                }
                /**
                 * <code>bytes key = 2;</code>
                 * @param value The key to set.
                 * @return This builder for chaining.
                 */
                public Builder setKey(com.google.protobuf.ByteString value) {
                    copyOnWrite();
                    instance.setKey(value);
                    return this;
                }
                /**
                 * <code>bytes key = 2;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearKey() {
                    copyOnWrite();
                    instance.clearKey();
                    return this;
                }

                // @@protoc_insertion_point(builder_scope:notifications.v1.Subscription.HmacKey)
            }
            @java.lang.Override
            @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
            protected final java.lang.Object dynamicMethod(
                    com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                    java.lang.Object arg0, java.lang.Object arg1) {
                switch (method) {
                    case NEW_MUTABLE_INSTANCE: {
                        return new org.xmtp.android.library.push.Service.Subscription.HmacKey();
                    }
                    case NEW_BUILDER: {
                        return new Builder();
                    }
                    case BUILD_MESSAGE_INFO: {
                        java.lang.Object[] objects = new java.lang.Object[] {
                                "thirtyDayPeriodsSinceEpoch_",
                                "key_",
                        };
                        java.lang.String info =
                                "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u000b\u0002\n" +
                                        "";
                        return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                    }
                    // fall through
                    case GET_DEFAULT_INSTANCE: {
                        return DEFAULT_INSTANCE;
                    }
                    case GET_PARSER: {
                        com.google.protobuf.Parser<org.xmtp.android.library.push.Service.Subscription.HmacKey> parser = PARSER;
                        if (parser == null) {
                            synchronized (org.xmtp.android.library.push.Service.Subscription.HmacKey.class) {
                                parser = PARSER;
                                if (parser == null) {
                                    parser =
                                            new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.Subscription.HmacKey>(
                                                    DEFAULT_INSTANCE);
                                    PARSER = parser;
                                }
                            }
                        }
                        return parser;
                    }
                    case GET_MEMOIZED_IS_INITIALIZED: {
                        return (byte) 1;
                    }
                    case SET_MEMOIZED_IS_INITIALIZED: {
                        return null;
                    }
                }
                throw new UnsupportedOperationException();
            }


            // @@protoc_insertion_point(class_scope:notifications.v1.Subscription.HmacKey)
            private static final org.xmtp.android.library.push.Service.Subscription.HmacKey DEFAULT_INSTANCE;
            static {
                HmacKey defaultInstance = new HmacKey();
                // New instances are implicitly immutable so no need to make
                // immutable.
                DEFAULT_INSTANCE = defaultInstance;
                com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                        HmacKey.class, defaultInstance);
            }

            public static org.xmtp.android.library.push.Service.Subscription.HmacKey getDefaultInstance() {
                return DEFAULT_INSTANCE;
            }

            private static volatile com.google.protobuf.Parser<HmacKey> PARSER;

            public static com.google.protobuf.Parser<HmacKey> parser() {
                return DEFAULT_INSTANCE.getParserForType();
            }
        }

        public static final int TOPIC_FIELD_NUMBER = 1;
        private java.lang.String topic_;
        /**
         * <code>string topic = 1;</code>
         * @return The topic.
         */
        @java.lang.Override
        public java.lang.String getTopic() {
            return topic_;
        }
        /**
         * <code>string topic = 1;</code>
         * @return The bytes for topic.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getTopicBytes() {
            return com.google.protobuf.ByteString.copyFromUtf8(topic_);
        }
        /**
         * <code>string topic = 1;</code>
         * @param value The topic to set.
         */
        private void setTopic(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();

            topic_ = value;
        }
        /**
         * <code>string topic = 1;</code>
         */
        private void clearTopic() {

            topic_ = getDefaultInstance().getTopic();
        }
        /**
         * <code>string topic = 1;</code>
         * @param value The bytes for topic to set.
         */
        private void setTopicBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            topic_ = value.toStringUtf8();

        }

        public static final int HMAC_KEYS_FIELD_NUMBER = 2;
        private com.google.protobuf.Internal.ProtobufList<org.xmtp.android.library.push.Service.Subscription.HmacKey> hmacKeys_;
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        @java.lang.Override
        public java.util.List<org.xmtp.android.library.push.Service.Subscription.HmacKey> getHmacKeysList() {
            return hmacKeys_;
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        public java.util.List<? extends org.xmtp.android.library.push.Service.Subscription.HmacKeyOrBuilder>
        getHmacKeysOrBuilderList() {
            return hmacKeys_;
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        @java.lang.Override
        public int getHmacKeysCount() {
            return hmacKeys_.size();
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        @java.lang.Override
        public org.xmtp.android.library.push.Service.Subscription.HmacKey getHmacKeys(int index) {
            return hmacKeys_.get(index);
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        public org.xmtp.android.library.push.Service.Subscription.HmacKeyOrBuilder getHmacKeysOrBuilder(
                int index) {
            return hmacKeys_.get(index);
        }
        private void ensureHmacKeysIsMutable() {
            com.google.protobuf.Internal.ProtobufList<org.xmtp.android.library.push.Service.Subscription.HmacKey> tmp = hmacKeys_;
            if (!tmp.isModifiable()) {
                hmacKeys_ =
                        com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
            }
        }

        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        private void setHmacKeys(
                int index, org.xmtp.android.library.push.Service.Subscription.HmacKey value) {
            value.getClass();
            ensureHmacKeysIsMutable();
            hmacKeys_.set(index, value);
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        private void addHmacKeys(org.xmtp.android.library.push.Service.Subscription.HmacKey value) {
            value.getClass();
            ensureHmacKeysIsMutable();
            hmacKeys_.add(value);
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        private void addHmacKeys(
                int index, org.xmtp.android.library.push.Service.Subscription.HmacKey value) {
            value.getClass();
            ensureHmacKeysIsMutable();
            hmacKeys_.add(index, value);
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        private void addAllHmacKeys(
                java.lang.Iterable<? extends org.xmtp.android.library.push.Service.Subscription.HmacKey> values) {
            ensureHmacKeysIsMutable();
            com.google.protobuf.AbstractMessageLite.addAll(
                    values, hmacKeys_);
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        private void clearHmacKeys() {
            hmacKeys_ = emptyProtobufList();
        }
        /**
         * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
         */
        private void removeHmacKeys(int index) {
            ensureHmacKeysIsMutable();
            hmacKeys_.remove(index);
        }

        public static final int IS_SILENT_FIELD_NUMBER = 3;
        private boolean isSilent_;
        /**
         * <code>bool is_silent = 3;</code>
         * @return The isSilent.
         */
        @java.lang.Override
        public boolean getIsSilent() {
            return isSilent_;
        }
        /**
         * <code>bool is_silent = 3;</code>
         * @param value The isSilent to set.
         */
        private void setIsSilent(boolean value) {

            isSilent_ = value;
        }
        /**
         * <code>bool is_silent = 3;</code>
         */
        private void clearIsSilent() {

            isSilent_ = false;
        }

        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.Subscription parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.Subscription prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * A subscription with associated metadata
         * </pre>
         *
         * Protobuf type {@code notifications.v1.Subscription}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.Subscription, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.Subscription)
                org.xmtp.android.library.push.Service.SubscriptionOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.Subscription.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }


            /**
             * <code>string topic = 1;</code>
             * @return The topic.
             */
            @java.lang.Override
            public java.lang.String getTopic() {
                return instance.getTopic();
            }
            /**
             * <code>string topic = 1;</code>
             * @return The bytes for topic.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getTopicBytes() {
                return instance.getTopicBytes();
            }
            /**
             * <code>string topic = 1;</code>
             * @param value The topic to set.
             * @return This builder for chaining.
             */
            public Builder setTopic(
                    java.lang.String value) {
                copyOnWrite();
                instance.setTopic(value);
                return this;
            }
            /**
             * <code>string topic = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearTopic() {
                copyOnWrite();
                instance.clearTopic();
                return this;
            }
            /**
             * <code>string topic = 1;</code>
             * @param value The bytes for topic to set.
             * @return This builder for chaining.
             */
            public Builder setTopicBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setTopicBytes(value);
                return this;
            }

            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            @java.lang.Override
            public java.util.List<org.xmtp.android.library.push.Service.Subscription.HmacKey> getHmacKeysList() {
                return java.util.Collections.unmodifiableList(
                        instance.getHmacKeysList());
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            @java.lang.Override
            public int getHmacKeysCount() {
                return instance.getHmacKeysCount();
            }/**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            @java.lang.Override
            public org.xmtp.android.library.push.Service.Subscription.HmacKey getHmacKeys(int index) {
                return instance.getHmacKeys(index);
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder setHmacKeys(
                    int index, org.xmtp.android.library.push.Service.Subscription.HmacKey value) {
                copyOnWrite();
                instance.setHmacKeys(index, value);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder setHmacKeys(
                    int index, org.xmtp.android.library.push.Service.Subscription.HmacKey.Builder builderForValue) {
                copyOnWrite();
                instance.setHmacKeys(index,
                        builderForValue.build());
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder addHmacKeys(org.xmtp.android.library.push.Service.Subscription.HmacKey value) {
                copyOnWrite();
                instance.addHmacKeys(value);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder addHmacKeys(
                    int index, org.xmtp.android.library.push.Service.Subscription.HmacKey value) {
                copyOnWrite();
                instance.addHmacKeys(index, value);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder addHmacKeys(
                    org.xmtp.android.library.push.Service.Subscription.HmacKey.Builder builderForValue) {
                copyOnWrite();
                instance.addHmacKeys(builderForValue.build());
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder addHmacKeys(
                    int index, org.xmtp.android.library.push.Service.Subscription.HmacKey.Builder builderForValue) {
                copyOnWrite();
                instance.addHmacKeys(index,
                        builderForValue.build());
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder addAllHmacKeys(
                    java.lang.Iterable<? extends org.xmtp.android.library.push.Service.Subscription.HmacKey> values) {
                copyOnWrite();
                instance.addAllHmacKeys(values);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder clearHmacKeys() {
                copyOnWrite();
                instance.clearHmacKeys();
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription.HmacKey hmac_keys = 2;</code>
             */
            public Builder removeHmacKeys(int index) {
                copyOnWrite();
                instance.removeHmacKeys(index);
                return this;
            }

            /**
             * <code>bool is_silent = 3;</code>
             * @return The isSilent.
             */
            @java.lang.Override
            public boolean getIsSilent() {
                return instance.getIsSilent();
            }
            /**
             * <code>bool is_silent = 3;</code>
             * @param value The isSilent to set.
             * @return This builder for chaining.
             */
            public Builder setIsSilent(boolean value) {
                copyOnWrite();
                instance.setIsSilent(value);
                return this;
            }
            /**
             * <code>bool is_silent = 3;</code>
             * @return This builder for chaining.
             */
            public Builder clearIsSilent() {
                copyOnWrite();
                instance.clearIsSilent();
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.Subscription)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.Subscription();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "topic_",
                            "hmacKeys_",
                            org.xmtp.android.library.push.Service.Subscription.HmacKey.class,
                            "isSilent_",
                    };
                    java.lang.String info =
                            "\u0000\u0003\u0000\u0000\u0001\u0003\u0003\u0000\u0001\u0000\u0001\u0208\u0002\u001b" +
                                    "\u0003\u0007";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.Subscription> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.Subscription.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.Subscription>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.Subscription)
        private static final org.xmtp.android.library.push.Service.Subscription DEFAULT_INSTANCE;
        static {
            Subscription defaultInstance = new Subscription();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    Subscription.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.Subscription getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<Subscription> PARSER;

        public static com.google.protobuf.Parser<Subscription> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }

    public interface SubscribeWithMetadataRequestOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.SubscribeWithMetadataRequest)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        java.lang.String getInstallationId();
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        com.google.protobuf.ByteString
        getInstallationIdBytes();

        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        java.util.List<org.xmtp.android.library.push.Service.Subscription>
        getSubscriptionsList();
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        org.xmtp.android.library.push.Service.Subscription getSubscriptions(int index);
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        int getSubscriptionsCount();
    }
    /**
     * <pre>
     * A request to subscribe to a list of topics and update the associated metadata
     * </pre>
     *
     * Protobuf type {@code notifications.v1.SubscribeWithMetadataRequest}
     */
    public  static final class SubscribeWithMetadataRequest extends
            com.google.protobuf.GeneratedMessageLite<
                    SubscribeWithMetadataRequest, SubscribeWithMetadataRequest.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.SubscribeWithMetadataRequest)
            SubscribeWithMetadataRequestOrBuilder {
        private SubscribeWithMetadataRequest() {
            installationId_ = "";
            subscriptions_ = emptyProtobufList();
        }
        public static final int INSTALLATION_ID_FIELD_NUMBER = 1;
        private java.lang.String installationId_;
        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        @java.lang.Override
        public java.lang.String getInstallationId() {
            return installationId_;
        }
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getInstallationIdBytes() {
            return com.google.protobuf.ByteString.copyFromUtf8(installationId_);
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The installationId to set.
         */
        private void setInstallationId(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();

            installationId_ = value;
        }
        /**
         * <code>string installation_id = 1;</code>
         */
        private void clearInstallationId() {

            installationId_ = getDefaultInstance().getInstallationId();
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The bytes for installationId to set.
         */
        private void setInstallationIdBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            installationId_ = value.toStringUtf8();

        }

        public static final int SUBSCRIPTIONS_FIELD_NUMBER = 2;
        private com.google.protobuf.Internal.ProtobufList<org.xmtp.android.library.push.Service.Subscription> subscriptions_;
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        @java.lang.Override
        public java.util.List<org.xmtp.android.library.push.Service.Subscription> getSubscriptionsList() {
            return subscriptions_;
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        public java.util.List<? extends org.xmtp.android.library.push.Service.SubscriptionOrBuilder>
        getSubscriptionsOrBuilderList() {
            return subscriptions_;
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        @java.lang.Override
        public int getSubscriptionsCount() {
            return subscriptions_.size();
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        @java.lang.Override
        public org.xmtp.android.library.push.Service.Subscription getSubscriptions(int index) {
            return subscriptions_.get(index);
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        public org.xmtp.android.library.push.Service.SubscriptionOrBuilder getSubscriptionsOrBuilder(
                int index) {
            return subscriptions_.get(index);
        }
        private void ensureSubscriptionsIsMutable() {
            com.google.protobuf.Internal.ProtobufList<org.xmtp.android.library.push.Service.Subscription> tmp = subscriptions_;
            if (!tmp.isModifiable()) {
                subscriptions_ =
                        com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
            }
        }

        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        private void setSubscriptions(
                int index, org.xmtp.android.library.push.Service.Subscription value) {
            value.getClass();
            ensureSubscriptionsIsMutable();
            subscriptions_.set(index, value);
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        private void addSubscriptions(org.xmtp.android.library.push.Service.Subscription value) {
            value.getClass();
            ensureSubscriptionsIsMutable();
            subscriptions_.add(value);
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        private void addSubscriptions(
                int index, org.xmtp.android.library.push.Service.Subscription value) {
            value.getClass();
            ensureSubscriptionsIsMutable();
            subscriptions_.add(index, value);
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        private void addAllSubscriptions(
                java.lang.Iterable<? extends org.xmtp.android.library.push.Service.Subscription> values) {
            ensureSubscriptionsIsMutable();
            com.google.protobuf.AbstractMessageLite.addAll(
                    values, subscriptions_);
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        private void clearSubscriptions() {
            subscriptions_ = emptyProtobufList();
        }
        /**
         * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
         */
        private void removeSubscriptions(int index) {
            ensureSubscriptionsIsMutable();
            subscriptions_.remove(index);
        }

        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * A request to subscribe to a list of topics and update the associated metadata
         * </pre>
         *
         * Protobuf type {@code notifications.v1.SubscribeWithMetadataRequest}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.SubscribeWithMetadataRequest)
                org.xmtp.android.library.push.Service.SubscribeWithMetadataRequestOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }


            /**
             * <code>string installation_id = 1;</code>
             * @return The installationId.
             */
            @java.lang.Override
            public java.lang.String getInstallationId() {
                return instance.getInstallationId();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return The bytes for installationId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getInstallationIdBytes() {
                return instance.getInstallationIdBytes();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationId(
                    java.lang.String value) {
                copyOnWrite();
                instance.setInstallationId(value);
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearInstallationId() {
                copyOnWrite();
                instance.clearInstallationId();
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The bytes for installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationIdBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setInstallationIdBytes(value);
                return this;
            }

            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            @java.lang.Override
            public java.util.List<org.xmtp.android.library.push.Service.Subscription> getSubscriptionsList() {
                return java.util.Collections.unmodifiableList(
                        instance.getSubscriptionsList());
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            @java.lang.Override
            public int getSubscriptionsCount() {
                return instance.getSubscriptionsCount();
            }/**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            @java.lang.Override
            public org.xmtp.android.library.push.Service.Subscription getSubscriptions(int index) {
                return instance.getSubscriptions(index);
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder setSubscriptions(
                    int index, org.xmtp.android.library.push.Service.Subscription value) {
                copyOnWrite();
                instance.setSubscriptions(index, value);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder setSubscriptions(
                    int index, org.xmtp.android.library.push.Service.Subscription.Builder builderForValue) {
                copyOnWrite();
                instance.setSubscriptions(index,
                        builderForValue.build());
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder addSubscriptions(org.xmtp.android.library.push.Service.Subscription value) {
                copyOnWrite();
                instance.addSubscriptions(value);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder addSubscriptions(
                    int index, org.xmtp.android.library.push.Service.Subscription value) {
                copyOnWrite();
                instance.addSubscriptions(index, value);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder addSubscriptions(
                    org.xmtp.android.library.push.Service.Subscription.Builder builderForValue) {
                copyOnWrite();
                instance.addSubscriptions(builderForValue.build());
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder addSubscriptions(
                    int index, org.xmtp.android.library.push.Service.Subscription.Builder builderForValue) {
                copyOnWrite();
                instance.addSubscriptions(index,
                        builderForValue.build());
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder addAllSubscriptions(
                    java.lang.Iterable<? extends org.xmtp.android.library.push.Service.Subscription> values) {
                copyOnWrite();
                instance.addAllSubscriptions(values);
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder clearSubscriptions() {
                copyOnWrite();
                instance.clearSubscriptions();
                return this;
            }
            /**
             * <code>repeated .notifications.v1.Subscription subscriptions = 2;</code>
             */
            public Builder removeSubscriptions(int index) {
                copyOnWrite();
                instance.removeSubscriptions(index);
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.SubscribeWithMetadataRequest)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "installationId_",
                            "subscriptions_",
                            org.xmtp.android.library.push.Service.Subscription.class,
                    };
                    java.lang.String info =
                            "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0001\u0000\u0001\u0208\u0002\u001b" +
                                    "";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.SubscribeWithMetadataRequest)
        private static final org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest DEFAULT_INSTANCE;
        static {
            SubscribeWithMetadataRequest defaultInstance = new SubscribeWithMetadataRequest();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    SubscribeWithMetadataRequest.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.SubscribeWithMetadataRequest getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<SubscribeWithMetadataRequest> PARSER;

        public static com.google.protobuf.Parser<SubscribeWithMetadataRequest> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }

    public interface SubscribeRequestOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.SubscribeRequest)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        java.lang.String getInstallationId();
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        com.google.protobuf.ByteString
        getInstallationIdBytes();

        /**
         * <code>repeated string topics = 2;</code>
         * @return A list containing the topics.
         */
        java.util.List<java.lang.String>
        getTopicsList();
        /**
         * <code>repeated string topics = 2;</code>
         * @return The count of topics.
         */
        int getTopicsCount();
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the element to return.
         * @return The topics at the given index.
         */
        java.lang.String getTopics(int index);
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the element to return.
         * @return The topics at the given index.
         */
        com.google.protobuf.ByteString
        getTopicsBytes(int index);
    }
    /**
     * <pre>
     * Subscribe to a list of topics
     * </pre>
     *
     * Protobuf type {@code notifications.v1.SubscribeRequest}
     */
    public  static final class SubscribeRequest extends
            com.google.protobuf.GeneratedMessageLite<
                    SubscribeRequest, SubscribeRequest.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.SubscribeRequest)
            SubscribeRequestOrBuilder {
        private SubscribeRequest() {
            installationId_ = "";
            topics_ = com.google.protobuf.GeneratedMessageLite.emptyProtobufList();
        }
        public static final int INSTALLATION_ID_FIELD_NUMBER = 1;
        private java.lang.String installationId_;
        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        @java.lang.Override
        public java.lang.String getInstallationId() {
            return installationId_;
        }
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getInstallationIdBytes() {
            return com.google.protobuf.ByteString.copyFromUtf8(installationId_);
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The installationId to set.
         */
        private void setInstallationId(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();

            installationId_ = value;
        }
        /**
         * <code>string installation_id = 1;</code>
         */
        private void clearInstallationId() {

            installationId_ = getDefaultInstance().getInstallationId();
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The bytes for installationId to set.
         */
        private void setInstallationIdBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            installationId_ = value.toStringUtf8();

        }

        public static final int TOPICS_FIELD_NUMBER = 2;
        private com.google.protobuf.Internal.ProtobufList<java.lang.String> topics_;
        /**
         * <code>repeated string topics = 2;</code>
         * @return A list containing the topics.
         */
        @java.lang.Override
        public java.util.List<java.lang.String> getTopicsList() {
            return topics_;
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @return The count of topics.
         */
        @java.lang.Override
        public int getTopicsCount() {
            return topics_.size();
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the element to return.
         * @return The topics at the given index.
         */
        @java.lang.Override
        public java.lang.String getTopics(int index) {
            return topics_.get(index);
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the topics at the given index.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getTopicsBytes(int index) {
            return com.google.protobuf.ByteString.copyFromUtf8(
                    topics_.get(index));
        }
        private void ensureTopicsIsMutable() {
            com.google.protobuf.Internal.ProtobufList<java.lang.String> tmp =
                    topics_;  if (!tmp.isModifiable()) {
                topics_ =
                        com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
            }
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index to set the value at.
         * @param value The topics to set.
         */
        private void setTopics(
                int index, java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();
            ensureTopicsIsMutable();
            topics_.set(index, value);
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param value The topics to add.
         */
        private void addTopics(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();
            ensureTopicsIsMutable();
            topics_.add(value);
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param values The topics to add.
         */
        private void addAllTopics(
                java.lang.Iterable<java.lang.String> values) {
            ensureTopicsIsMutable();
            com.google.protobuf.AbstractMessageLite.addAll(
                    values, topics_);
        }
        /**
         * <code>repeated string topics = 2;</code>
         */
        private void clearTopics() {
            topics_ = com.google.protobuf.GeneratedMessageLite.emptyProtobufList();
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param value The bytes of the topics to add.
         */
        private void addTopicsBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            ensureTopicsIsMutable();
            topics_.add(value.toStringUtf8());
        }

        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.SubscribeRequest parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.SubscribeRequest prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * Subscribe to a list of topics
         * </pre>
         *
         * Protobuf type {@code notifications.v1.SubscribeRequest}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.SubscribeRequest, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.SubscribeRequest)
                org.xmtp.android.library.push.Service.SubscribeRequestOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.SubscribeRequest.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }


            /**
             * <code>string installation_id = 1;</code>
             * @return The installationId.
             */
            @java.lang.Override
            public java.lang.String getInstallationId() {
                return instance.getInstallationId();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return The bytes for installationId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getInstallationIdBytes() {
                return instance.getInstallationIdBytes();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationId(
                    java.lang.String value) {
                copyOnWrite();
                instance.setInstallationId(value);
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearInstallationId() {
                copyOnWrite();
                instance.clearInstallationId();
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The bytes for installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationIdBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setInstallationIdBytes(value);
                return this;
            }

            /**
             * <code>repeated string topics = 2;</code>
             * @return A list containing the topics.
             */
            @java.lang.Override
            public java.util.List<java.lang.String>
            getTopicsList() {
                return java.util.Collections.unmodifiableList(
                        instance.getTopicsList());
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @return The count of topics.
             */
            @java.lang.Override
            public int getTopicsCount() {
                return instance.getTopicsCount();
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param index The index of the element to return.
             * @return The topics at the given index.
             */
            @java.lang.Override
            public java.lang.String getTopics(int index) {
                return instance.getTopics(index);
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param index The index of the value to return.
             * @return The bytes of the topics at the given index.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getTopicsBytes(int index) {
                return instance.getTopicsBytes(index);
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param index The index to set the value at.
             * @param value The topics to set.
             * @return This builder for chaining.
             */
            public Builder setTopics(
                    int index, java.lang.String value) {
                copyOnWrite();
                instance.setTopics(index, value);
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param value The topics to add.
             * @return This builder for chaining.
             */
            public Builder addTopics(
                    java.lang.String value) {
                copyOnWrite();
                instance.addTopics(value);
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param values The topics to add.
             * @return This builder for chaining.
             */
            public Builder addAllTopics(
                    java.lang.Iterable<java.lang.String> values) {
                copyOnWrite();
                instance.addAllTopics(values);
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @return This builder for chaining.
             */
            public Builder clearTopics() {
                copyOnWrite();
                instance.clearTopics();
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param value The bytes of the topics to add.
             * @return This builder for chaining.
             */
            public Builder addTopicsBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.addTopicsBytes(value);
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.SubscribeRequest)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.SubscribeRequest();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "installationId_",
                            "topics_",
                    };
                    java.lang.String info =
                            "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0001\u0000\u0001\u0208\u0002\u021a" +
                                    "";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.SubscribeRequest> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.SubscribeRequest.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.SubscribeRequest>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.SubscribeRequest)
        private static final org.xmtp.android.library.push.Service.SubscribeRequest DEFAULT_INSTANCE;
        static {
            SubscribeRequest defaultInstance = new SubscribeRequest();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    SubscribeRequest.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.SubscribeRequest getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<SubscribeRequest> PARSER;

        public static com.google.protobuf.Parser<SubscribeRequest> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }

    public interface UnsubscribeRequestOrBuilder extends
            // @@protoc_insertion_point(interface_extends:notifications.v1.UnsubscribeRequest)
            com.google.protobuf.MessageLiteOrBuilder {

        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        java.lang.String getInstallationId();
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        com.google.protobuf.ByteString
        getInstallationIdBytes();

        /**
         * <code>repeated string topics = 2;</code>
         * @return A list containing the topics.
         */
        java.util.List<java.lang.String>
        getTopicsList();
        /**
         * <code>repeated string topics = 2;</code>
         * @return The count of topics.
         */
        int getTopicsCount();
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the element to return.
         * @return The topics at the given index.
         */
        java.lang.String getTopics(int index);
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the element to return.
         * @return The topics at the given index.
         */
        com.google.protobuf.ByteString
        getTopicsBytes(int index);
    }
    /**
     * <pre>
     * Unsubscribe from a list of topics
     * </pre>
     *
     * Protobuf type {@code notifications.v1.UnsubscribeRequest}
     */
    public  static final class UnsubscribeRequest extends
            com.google.protobuf.GeneratedMessageLite<
                    UnsubscribeRequest, UnsubscribeRequest.Builder> implements
            // @@protoc_insertion_point(message_implements:notifications.v1.UnsubscribeRequest)
            UnsubscribeRequestOrBuilder {
        private UnsubscribeRequest() {
            installationId_ = "";
            topics_ = com.google.protobuf.GeneratedMessageLite.emptyProtobufList();
        }
        public static final int INSTALLATION_ID_FIELD_NUMBER = 1;
        private java.lang.String installationId_;
        /**
         * <code>string installation_id = 1;</code>
         * @return The installationId.
         */
        @java.lang.Override
        public java.lang.String getInstallationId() {
            return installationId_;
        }
        /**
         * <code>string installation_id = 1;</code>
         * @return The bytes for installationId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getInstallationIdBytes() {
            return com.google.protobuf.ByteString.copyFromUtf8(installationId_);
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The installationId to set.
         */
        private void setInstallationId(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();

            installationId_ = value;
        }
        /**
         * <code>string installation_id = 1;</code>
         */
        private void clearInstallationId() {

            installationId_ = getDefaultInstance().getInstallationId();
        }
        /**
         * <code>string installation_id = 1;</code>
         * @param value The bytes for installationId to set.
         */
        private void setInstallationIdBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            installationId_ = value.toStringUtf8();

        }

        public static final int TOPICS_FIELD_NUMBER = 2;
        private com.google.protobuf.Internal.ProtobufList<java.lang.String> topics_;
        /**
         * <code>repeated string topics = 2;</code>
         * @return A list containing the topics.
         */
        @java.lang.Override
        public java.util.List<java.lang.String> getTopicsList() {
            return topics_;
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @return The count of topics.
         */
        @java.lang.Override
        public int getTopicsCount() {
            return topics_.size();
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the element to return.
         * @return The topics at the given index.
         */
        @java.lang.Override
        public java.lang.String getTopics(int index) {
            return topics_.get(index);
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the topics at the given index.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getTopicsBytes(int index) {
            return com.google.protobuf.ByteString.copyFromUtf8(
                    topics_.get(index));
        }
        private void ensureTopicsIsMutable() {
            com.google.protobuf.Internal.ProtobufList<java.lang.String> tmp =
                    topics_;  if (!tmp.isModifiable()) {
                topics_ =
                        com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
            }
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param index The index to set the value at.
         * @param value The topics to set.
         */
        private void setTopics(
                int index, java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();
            ensureTopicsIsMutable();
            topics_.set(index, value);
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param value The topics to add.
         */
        private void addTopics(
                java.lang.String value) {
            java.lang.Class<?> valueClass = value.getClass();
            ensureTopicsIsMutable();
            topics_.add(value);
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param values The topics to add.
         */
        private void addAllTopics(
                java.lang.Iterable<java.lang.String> values) {
            ensureTopicsIsMutable();
            com.google.protobuf.AbstractMessageLite.addAll(
                    values, topics_);
        }
        /**
         * <code>repeated string topics = 2;</code>
         */
        private void clearTopics() {
            topics_ = com.google.protobuf.GeneratedMessageLite.emptyProtobufList();
        }
        /**
         * <code>repeated string topics = 2;</code>
         * @param value The bytes of the topics to add.
         */
        private void addTopicsBytes(
                com.google.protobuf.ByteString value) {
            checkByteStringIsUtf8(value);
            ensureTopicsIsMutable();
            topics_.add(value.toStringUtf8());
        }

        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, data, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input);
        }
        public static org.xmtp.android.library.push.Service.UnsubscribeRequest parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageLite.parseFrom(
                    DEFAULT_INSTANCE, input, extensionRegistry);
        }

        public static Builder newBuilder() {
            return (Builder) DEFAULT_INSTANCE.createBuilder();
        }
        public static Builder newBuilder(org.xmtp.android.library.push.Service.UnsubscribeRequest prototype) {
            return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
        }

        /**
         * <pre>
         * Unsubscribe from a list of topics
         * </pre>
         *
         * Protobuf type {@code notifications.v1.UnsubscribeRequest}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageLite.Builder<
                        org.xmtp.android.library.push.Service.UnsubscribeRequest, Builder> implements
                // @@protoc_insertion_point(builder_implements:notifications.v1.UnsubscribeRequest)
                org.xmtp.android.library.push.Service.UnsubscribeRequestOrBuilder {
            // Construct using org.xmtp.android.library.push.Service.UnsubscribeRequest.newBuilder()
            private Builder() {
                super(DEFAULT_INSTANCE);
            }


            /**
             * <code>string installation_id = 1;</code>
             * @return The installationId.
             */
            @java.lang.Override
            public java.lang.String getInstallationId() {
                return instance.getInstallationId();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return The bytes for installationId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getInstallationIdBytes() {
                return instance.getInstallationIdBytes();
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationId(
                    java.lang.String value) {
                copyOnWrite();
                instance.setInstallationId(value);
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearInstallationId() {
                copyOnWrite();
                instance.clearInstallationId();
                return this;
            }
            /**
             * <code>string installation_id = 1;</code>
             * @param value The bytes for installationId to set.
             * @return This builder for chaining.
             */
            public Builder setInstallationIdBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.setInstallationIdBytes(value);
                return this;
            }

            /**
             * <code>repeated string topics = 2;</code>
             * @return A list containing the topics.
             */
            @java.lang.Override
            public java.util.List<java.lang.String>
            getTopicsList() {
                return java.util.Collections.unmodifiableList(
                        instance.getTopicsList());
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @return The count of topics.
             */
            @java.lang.Override
            public int getTopicsCount() {
                return instance.getTopicsCount();
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param index The index of the element to return.
             * @return The topics at the given index.
             */
            @java.lang.Override
            public java.lang.String getTopics(int index) {
                return instance.getTopics(index);
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param index The index of the value to return.
             * @return The bytes of the topics at the given index.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getTopicsBytes(int index) {
                return instance.getTopicsBytes(index);
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param index The index to set the value at.
             * @param value The topics to set.
             * @return This builder for chaining.
             */
            public Builder setTopics(
                    int index, java.lang.String value) {
                copyOnWrite();
                instance.setTopics(index, value);
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param value The topics to add.
             * @return This builder for chaining.
             */
            public Builder addTopics(
                    java.lang.String value) {
                copyOnWrite();
                instance.addTopics(value);
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param values The topics to add.
             * @return This builder for chaining.
             */
            public Builder addAllTopics(
                    java.lang.Iterable<java.lang.String> values) {
                copyOnWrite();
                instance.addAllTopics(values);
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @return This builder for chaining.
             */
            public Builder clearTopics() {
                copyOnWrite();
                instance.clearTopics();
                return this;
            }
            /**
             * <code>repeated string topics = 2;</code>
             * @param value The bytes of the topics to add.
             * @return This builder for chaining.
             */
            public Builder addTopicsBytes(
                    com.google.protobuf.ByteString value) {
                copyOnWrite();
                instance.addTopicsBytes(value);
                return this;
            }

            // @@protoc_insertion_point(builder_scope:notifications.v1.UnsubscribeRequest)
        }
        @java.lang.Override
        @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
        protected final java.lang.Object dynamicMethod(
                com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
                java.lang.Object arg0, java.lang.Object arg1) {
            switch (method) {
                case NEW_MUTABLE_INSTANCE: {
                    return new org.xmtp.android.library.push.Service.UnsubscribeRequest();
                }
                case NEW_BUILDER: {
                    return new Builder();
                }
                case BUILD_MESSAGE_INFO: {
                    java.lang.Object[] objects = new java.lang.Object[] {
                            "installationId_",
                            "topics_",
                    };
                    java.lang.String info =
                            "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0001\u0000\u0001\u0208\u0002\u021a" +
                                    "";
                    return newMessageInfo(DEFAULT_INSTANCE, info, objects);
                }
                // fall through
                case GET_DEFAULT_INSTANCE: {
                    return DEFAULT_INSTANCE;
                }
                case GET_PARSER: {
                    com.google.protobuf.Parser<org.xmtp.android.library.push.Service.UnsubscribeRequest> parser = PARSER;
                    if (parser == null) {
                        synchronized (org.xmtp.android.library.push.Service.UnsubscribeRequest.class) {
                            parser = PARSER;
                            if (parser == null) {
                                parser =
                                        new DefaultInstanceBasedParser<org.xmtp.android.library.push.Service.UnsubscribeRequest>(
                                                DEFAULT_INSTANCE);
                                PARSER = parser;
                            }
                        }
                    }
                    return parser;
                }
                case GET_MEMOIZED_IS_INITIALIZED: {
                    return (byte) 1;
                }
                case SET_MEMOIZED_IS_INITIALIZED: {
                    return null;
                }
            }
            throw new UnsupportedOperationException();
        }


        // @@protoc_insertion_point(class_scope:notifications.v1.UnsubscribeRequest)
        private static final org.xmtp.android.library.push.Service.UnsubscribeRequest DEFAULT_INSTANCE;
        static {
            UnsubscribeRequest defaultInstance = new UnsubscribeRequest();
            // New instances are implicitly immutable so no need to make
            // immutable.
            DEFAULT_INSTANCE = defaultInstance;
            com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
                    UnsubscribeRequest.class, defaultInstance);
        }

        public static org.xmtp.android.library.push.Service.UnsubscribeRequest getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static volatile com.google.protobuf.Parser<UnsubscribeRequest> PARSER;

        public static com.google.protobuf.Parser<UnsubscribeRequest> parser() {
            return DEFAULT_INSTANCE.getParserForType();
        }
    }


    static {
    }

    // @@protoc_insertion_point(outer_class_scope)
}
