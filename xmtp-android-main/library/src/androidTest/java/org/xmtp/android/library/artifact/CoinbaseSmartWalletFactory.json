[{"type": "constructor", "inputs": [{"name": "erc4337", "type": "address", "internalType": "address"}], "stateMutability": "payable"}, {"type": "function", "name": "createAccount", "inputs": [{"name": "owners", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "account", "type": "address", "internalType": "contract CoinbaseSmartWallet"}], "stateMutability": "payable"}, {"type": "function", "name": "get<PERSON><PERSON><PERSON>", "inputs": [{"name": "owners", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "predicted", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "implementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initCodeHash", "inputs": [], "outputs": [{"name": "result", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "error", "name": "OwnerRequired", "inputs": []}]