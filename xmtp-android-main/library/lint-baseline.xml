<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.0.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.0.0)" variant="all" version="8.0.0">

    <issue
        id="NewA<PERSON>"
        message="Call requires API level 33 (current min is 23): `java.lang.ref.Cleaner#create`"
        errorLine1="    val cleaner = java.lang.ref.Cleaner.create()"
        errorLine2="                                        ~~~~~~">
        <location
            file="src/main/java/xmtpv3.kt"
            line="2270"
            column="41"/>
    </issue>

    <issue
        id="NewApi"
        message="Call requires API level 33 (current min is 23): `java.lang.ref.Cleaner#register`"
        errorLine1="        JavaLangRefCleanable(cleaner.register(value, cleanUpTask))"
        errorLine2="                                     ~~~~~~~~">
        <location
            file="src/main/java/xmtpv3.kt"
            line="2273"
            column="38"/>
    </issue>

    <issue
        id="NewApi"
        message="Call requires API level 33 (current min is 23): `java.lang.ref.Cleaner.Cleanable#clean`"
        errorLine1="    override fun clean() = cleanable.clean()"
        errorLine2="                                     ~~~~~">
        <location
            file="src/main/java/xmtpv3.kt"
            line="2279"
            column="38"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="    val formatter = SimpleDateFormat(&quot;EEE, dd MMM yyyy HH:mm:ss &apos;GMT&apos;&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/xmtp/android/library/messages/Signature.kt"
            line="53"
            column="21"/>
    </issue>

</issues>
