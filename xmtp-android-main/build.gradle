//buildscript {
//    repositories {
//        google()
//        mavenCentral()
//    }
//    dependencies {
//        classpath 'com.google.gms:google-services:4.3.15'
//    }
//}

plugins {
    id 'com.android.application' version '8.4.0' apply false
    id 'com.android.library' version '8.4.0' apply false
    id 'org.jetbrains.kotlin.android' version '2.0.0' apply false
    id 'io.github.gradle-nexus.publish-plugin' version "1.2.0"
    id "org.jetbrains.dokka" version "1.8.10"
}

group = "org.xmtp"
version = System.getenv("RELEASE_VERSION")

nexusPublishing {
    repositories {
        sonatype {
            nexusUrl.set(uri("https://ossrh-staging-api.central.sonatype.com/service/local/"))
            username = System.getenv("MAVEN_USERNAME")
            password = System.getenv("MAVEN_PASSWORD")
            stagingProfileId = System.getenv("MAVEN_PROFILE_ID")
        }
    }
}