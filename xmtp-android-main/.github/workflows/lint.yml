name: Lint
on:
  push:
    branches:
      - main
  pull_request:

env:
  GPR_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  GPR_USER: ${{ secrets.GITHUB_ACTOR }}

jobs:
  static-analysis:
    name: Static Analysis
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          distribution: 'adopt'
          java-version: '17'
      - name: Gradle Run ktlint
        run: ./gradlew ktlintCheck --continue
      - uses: actions/upload-artifact@v4
        name: Upload ktlint report
        if: ${{ failure() }}
        with:
          name: ktlint
          path: '**/build/reports/ktlint/'

      - name: Gradle Android lint library
        run: ./gradlew :library:lintDebug
      - uses: actions/upload-artifact@v4
        name: Upload library lint report
        if: ${{ failure() }}
        with:
          name: lint
          path: 'app/build/reports/library/lint-results-**.html'
