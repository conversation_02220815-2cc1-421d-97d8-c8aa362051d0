name: Release XMTP Android Package
on:
  push:
    branches:
      - release

jobs:
  library:
    name: Release Library
    runs-on: ubuntu-latest
    steps:
      - name: Checkout project sources
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Configure JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'adopt'
          java-version: '17'
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3
      - name: Run build with Gradle Wrapper
        run: ./gradlew build
      - name: Tag version 4.4.0
        run: |
          git tag 4.4.0
          git push origin 4.4.0
      - name: Create a GitHub release for version 4.4.0
        uses: ncipollo/release-action@v1
        with:
          tag: "4.4.0"
          name: "v4.4.0"
          body: " "

      - name: Gradle Publish
        env:
          RELEASE_VERSION: "4.4.0"
          MAVEN_USERNAME: ${{ secrets.OSSRH_USERNAME }}
          MAVEN_PASSWORD: ${{ secrets.OSSRH_TOKEN }}
          SIGN_KEY: ${{ secrets.OSSRH_GPG_SECRET_KEY }}
          SIGN_PASSWORD: ${{ secrets.OSSRH_GPG_SECRET_KEY_PASSWORD }}
          MAVEN_PROFILE_ID: ${{ secrets.MAVEN_PROFILE_ID }}
        run: ./gradlew publishToSonatype closeAndReleaseSonatypeStagingRepository