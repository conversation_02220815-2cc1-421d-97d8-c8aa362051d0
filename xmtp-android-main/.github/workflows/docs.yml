name: Publish Docs
on:
  push:
    branches:
      - main
permissions:
  contents: write
jobs:
  publish-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'adopt'
          java-version: '17'
      - name: Build documentation
        run: ./gradlew dokkaHtml
      - name: Publish documentation
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: library/build/dokka/html