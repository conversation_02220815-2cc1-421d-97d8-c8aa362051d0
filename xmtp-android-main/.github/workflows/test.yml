name: Test
on:
  push:
    branches:
      - main
  pull_request:

env:
  GPR_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  GPR_USER: ${{ secrets.GITHUB_ACTOR }}

jobs:
  library-unit:
    name: Library (Unit Tests)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout project sources
        uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: 'adopt'
          java-version: '17'
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3
      - name: Start Docker containers
        run: dev/up
      - name: Gradle Run Unit Tests
        run: ./gradlew library:testDebug
  library-integration:
    name: Library (Integration Tests)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Configure JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'adopt'
          java-version: '17'
      - name: Enable KVM group perms
        run: |
          echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
          sudo udevadm control --reload-rules
          sudo udevadm trigger --name-match=kvm
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3
      - name: Start Docker containers
        run: dev/up
      - name: Gradle Run Integration Tests
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: 29
          script: ./gradlew connectedCheck

