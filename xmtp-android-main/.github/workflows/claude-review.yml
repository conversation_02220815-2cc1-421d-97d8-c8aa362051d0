name: Claude <PERSON> Review

on:
  pull_request:
    types: [opened, synchronize]
    # Optional: Only run on specific file changes
    # paths:
    #   - "library/**/*.kt"
    #   - "library/**/*.java"
    #   - "example/**/*.kt"
    #   - "example/**/*.java"

jobs:
  claude-review:
    # Optional: Filter by PR author
    # if: |
    #   github.event.pull_request.user.login == 'external-contributor' ||
    #   github.event.pull_request.user.login == 'new-developer' ||
    #   github.event.pull_request.author_association == 'FIRST_TIME_CONTRIBUTOR'

    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      issues: write
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Run Claude Code Review
        id: claude-review
        continue-on-error: true
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}

          model: "claude-opus-4-1-20250805"

          # Direct prompt for automated review (no @claude mention needed)
          direct_prompt: |
            Please review this pull request for the XMTP Android SDK and provide feedback on:
            - Code quality and Kotlin/Android best practices
            - Potential bugs or issues
            - Performance considerations for mobile devices
            - Security concerns specific to messaging/crypto operations
            - Test coverage for the changes
            - Proper null safety and coroutine usage
            Be constructive and helpful in your feedback.
